/* pages/venue-list/venue-list.wxss */

/* 容器样式 */
.venue-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
}

.search-input-container icon {
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
  font-weight: bold;
}

.map-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  width: 80rpx;
}

.map-icon {
  width: 40rpx;
  height: 40rpx;
}

.map-btn text {
  font-size: 22rpx;
  color: #333;
  margin-top: 4rpx;
}

/* 筛选栏容器 */
.filter-section {
  position: relative;
  z-index: 10;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  height: 80rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
  z-index: 11; /* Ensure it's above other content */
}

.filter-item text.active {
  color: #E74C3C;
  font-weight: bold;
}

.dropdown-icon {
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  margin-left: 8rpx;
}

.dropdown-icon.down {
  border-top: 10rpx solid #666;
  border-bottom: none;
}

.dropdown-icon.up {
  border-bottom: 10rpx solid #E74C3C;
  border-top: none;
}

/* 下拉菜单样式 */
.dropdown-menu {
  position: absolute;
  top: 80rpx; /* Position below the filter bar (height of filter-bar) */
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.dropdown-menu.show {
  max-height: 600rpx;
}

.dropdown-item {
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-item.active {
  color: #E74C3C;
}

.check-icon {
  color: #E74C3C;
  font-weight: bold;
}

.sport-type-list, .venue-type-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.sport-type-item, .venue-type-item {
  width: 25%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.sport-type-item.active, .venue-type-item.active {
  color: #E74C3C;
  font-weight: bold;
}

/* 场馆类型项目样式 - 因为名称较长，调整宽度 */
.venue-type-item {
  width: 50%;
}

/* 场馆列表样式 */
.venues-scroll-view {
  flex: 1;
  background-color: #f5f5f5;
}

.venue-list {
  padding: 20rpx;
}

.venue-card {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx; /* 增加卡片圆角 */
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
  align-items: center; /* 添加垂直居中 */
  padding-right: 10rpx; /* 右侧添加内边距，平衡左侧图片的边距 */
}

.venue-image {
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
  display: block; /* 确保图片正确显示 */
  object-fit: cover; /* 保持图片比例并填充容器 */
  border-radius: 10rpx; /* 添加圆角 */
  margin: 10rpx; /* 添加边距，使圆角效果更明显 */
}

.venue-info {
  flex: 1;
  padding: 20rpx;
  position: relative;
}

.venue-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.venue-type {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.venue-distance {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

/* 运动类型标签样式 */
.venue-sports {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.sport-tag {
  font-size: 22rpx;
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 0 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-tag {
  background-color: rgba(231, 76, 60, 0.1);
}

/* 加载中样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 无结果提示样式 */
.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 28rpx;
}

.no-result icon {
  margin-bottom: 20rpx;
}

/* 筛选面板样式 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: -80%;
  width: 80%;
  height: 100%;
  background-color: #fff;
  z-index: 101;
  display: flex;
  flex-direction: column;
  transition: right 0.3s ease;
}

.filter-panel.show {
  right: 0;
}

.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-panel-close {
  font-size: 40rpx;
  color: #999;
}

.filter-panel-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
}

.filter-tag {
  font-size: 26rpx;
  color: #333;
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.filter-tag.active {
  color: #fff;
  background-color: #E74C3C;
}

.filter-panel-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.btn-reset, .btn-confirm {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.btn-reset {
  color: #333;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.btn-confirm {
  color: #fff;
  background-color: #E74C3C;
}
