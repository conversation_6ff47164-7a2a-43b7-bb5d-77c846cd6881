// pages/my-orders/my-orders.js
import { getOrders } from '../../utils/storage';

Page({
  data: {
    orders: [],
    filteredOrders: [],
    loading: true,
    currentTab: 'all', // 当前选中的标签: all, pending, processing, refunded, completed
  },

  // 获取订单状态CSS类
  getOrderStatusClass: function(order) {
    if (order.status === 'processing' || order.statusText === '订单进行中' || order.statusText === '进行中') {
      return 'processing';
    } else if (order.status === 'pending' || order.statusText === '待支付') {
      return 'pending';
    } else if (order.status === 'completed' || order.statusText === '已完成') {
      return 'completed';
    } else if (order.status === 'refunded' || order.statusText === '已退款' || order.statusText === '已取消') {
      return 'refunded';
    } else {
      return 'processing';
    }
  },

  onLoad: function() {
    this.loadOrders();
  },

  onShow: function() {
    // 每次页面显示时重新加载订单，确保数据最新
    this.loadOrders();
  },

  // 加载订单数据
  loadOrders: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 从本地存储获取订单数据
    let orders = getOrders();

    // 处理订单状态，确保CSS类正确
    orders = orders.map(order => {
      // 根据状态文本设置正确的状态值
      if (order.statusText === '订单进行中' || order.statusText === '进行中') {
        order.status = 'processing';
      } else if (order.statusText === '待支付') {
        order.status = 'pending';
      } else if (order.statusText === '已完成') {
        order.status = 'completed';
      } else if (order.statusText === '已退款' || order.statusText === '已取消') {
        order.status = 'refunded';
      }
      return order;
    });

    // 按照下单时间倒序排序（最新的订单在前面）
    orders.sort((a, b) => {
      const timeA = new Date(a.orderInfo.orderTime).getTime();
      const timeB = new Date(b.orderInfo.orderTime).getTime();
      return timeB - timeA;
    });

    this.setData({
      orders,
      filteredOrders: orders,
      loading: false,
      currentTab: 'all'
    });

    wx.hideLoading();
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab === this.data.currentTab) return;

    let filteredOrders = [];

    // 获取订单状态文本映射
    const statusTextMap = {
      'pending': ['待支付', '等待支付'],
      'processing': ['订单进行中', '进行中'],
      'refunded': ['已退款', '已取消'],
      'completed': ['已完成']
    };

    switch(tab) {
      case 'all':
        filteredOrders = this.data.orders;
        break;
      case 'pending':
        filteredOrders = this.data.orders.filter(order => {
          return order.status === 'pending' ||
                 (statusTextMap.pending.includes(order.statusText));
        });
        break;
      case 'processing':
        filteredOrders = this.data.orders.filter(order => {
          return order.status === 'processing' ||
                 (statusTextMap.processing.includes(order.statusText));
        });
        break;
      case 'refunded':
        filteredOrders = this.data.orders.filter(order => {
          return order.status === 'refunded' ||
                 (statusTextMap.refunded.includes(order.statusText));
        });
        break;
      case 'completed':
        filteredOrders = this.data.orders.filter(order => {
          return order.status === 'completed' ||
                 (statusTextMap.completed.includes(order.statusText));
        });
        break;
      default:
        filteredOrders = this.data.orders;
    }

    this.setData({
      currentTab: tab,
      filteredOrders
    });
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}`
    });
  },

  // 扫码签到
  scanToEnter: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}&action=scan`
    });
  },

  // 邀请好友
  inviteFriends: function() {
    wx.showToast({
      title: '邀请好友功能开发中',
      icon: 'none'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
});
