<!--pages/club-detail/club-detail.wxml-->
<view class="club-detail-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">社团详情</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 社团信息 -->
  <view class="club-info-container" wx:if="{{!loading && club}}">
    <view class="club-header">
      <image class="club-logo" src="{{club.logo}}" mode="aspectFill" wx:if="{{club.logo}}"></image>
      <view class="club-logo-placeholder" wx:else>社团</view>
      <view class="club-basic-info">
        <view class="club-name-row">
          <view class="club-name">{{club.name}}</view>
          <!-- 社团管理图标 -->
          <view class="management-icon-small" wx:if="{{isCreator || isAdmin}}" bindtap="navigateToManagement">⚙️</view>
        </view>
        <view class="club-org">所属单位：{{club.orgName}}</view>
        <view class="club-sports">
          <text wx:for="{{club.sportTypes}}" wx:key="*this" class="sport-tag">{{item}}</text>
        </view>
        <view class="club-member-count">
          <text>成员：{{club.memberCount}}人</text>
        </view>
      </view>
    </view>

    <view class="club-description">
      <text>{{club.description}}</text>
    </view>

    <!-- 内容选项卡 -->
    <view class="tab-container">
      <view class="tab-header">
        <view class="tab-item {{currentTab === 'members' ? 'active' : ''}}" bindtap="switchTab" data-tab="members">成员</view>
        <view class="tab-item {{currentTab === 'announcements' ? 'active' : ''}}" bindtap="switchTab" data-tab="announcements">公告</view>
        <view class="tab-item {{currentTab === 'activities' ? 'active' : ''}}" bindtap="switchTab" data-tab="activities">动态</view>
      </view>

      <!-- 成员列表 -->
      <view class="tab-content" wx:if="{{currentTab === 'members'}}">
        <view class="member-list">
          <view class="member-item" wx:for="{{members}}" wx:key="id">
            <image class="member-avatar" src="{{item.avatar}}" mode="aspectFill" wx:if="{{item.avatar}}"></image>
            <view class="member-avatar-placeholder" wx:else>{{item.name[0]}}</view>
            <view class="member-info">
              <view class="member-name-row">
                <view class="member-name">{{item.name}}</view>
                <view class="member-role-container">
                  <view class="member-role" wx:if="{{item.isCreator}}">团长</view>
                  <view class="member-role admin" wx:elif="{{item.isAdmin}}">管理员</view>
                </view>
              </view>
              <view class="member-join-time">加入时间：{{item.joinTimeFormatted}}</view>
            </view>

            <!-- 管理操作按钮 -->
            <view class="member-actions" wx:if="{{(isCreator || isAdmin) && !item.isCreator && item.id !== userId}}">
              <!-- 团长可以设置/取消管理员 -->
              <button class="action-btn set-admin" wx:if="{{isCreator && !item.isAdmin}}" catchtap="setMemberAsAdmin" data-id="{{item.id}}">设为管理员</button>
              <button class="action-btn remove-admin" wx:if="{{isCreator && item.isAdmin}}" catchtap="removeMemberAsAdmin" data-id="{{item.id}}">取消管理员</button>

              <!-- 团长和管理员都可以移除普通成员 -->
              <button class="action-btn remove-member" wx:if="{{item.isRegularMember || (isCreator && item.isAdmin)}}" catchtap="removeMember" data-id="{{item.id}}">移除</button>
            </view>
          </view>

          <!-- 待审批成员 -->
          <view class="pending-members" wx:if="{{(isCreator || isAdmin) && pendingMembers.length > 0}}">
            <view class="pending-title">待审批成员</view>
            <view class="member-item pending" wx:for="{{pendingMembers}}" wx:key="id">
              <view class="pending-badge">
                <view class="pending-badge-text">待审批</view>
              </view>
              <view class="member-info">
                <view class="member-name">{{item.name}}</view>
                <view class="member-join-time">申请时间：{{item.joinTimeFormatted || '2025-05-05 18:00'}}</view>
              </view>
              <view class="approval-actions">
                <button class="approve-btn" bindtap="approveMember" data-id="{{item.id}}">通过</button>
                <button class="reject-btn" bindtap="rejectMember" data-id="{{item.id}}">拒绝</button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 公告列表 -->
      <view class="tab-content" wx:if="{{currentTab === 'announcements'}}">
        <view class="announcement-list">
          <view class="empty-tip" wx:if="{{announcements.length === 0}}">
            <text>暂无公告</text>
          </view>
          <view class="announcement-item" wx:for="{{announcements}}" wx:key="id">
            <view class="announcement-header">
              <view class="announcement-title">{{item.title}}</view>
              <view class="announcement-time">{{item.timeFormatted}}</view>
            </view>
            <view class="announcement-content">{{item.content}}</view>
          </view>
        </view>

        <!-- 发布公告按钮 -->
        <view class="publish-announcement" wx:if="{{isCreator || isAdmin}}" bindtap="showAnnouncementModal">
          <text>发布公告</text>
        </view>
      </view>

      <!-- 动态列表 -->
      <view class="tab-content" wx:if="{{currentTab === 'activities'}}">
        <view class="activity-list">
          <view class="empty-tip" wx:if="{{activities.length === 0}}">
            <text>暂无动态</text>
          </view>
          <view class="activity-item" wx:for="{{activities}}" wx:key="id">
            <view class="activity-header">
              <image class="activity-avatar" src="{{item.userAvatar}}" mode="aspectFill" wx:if="{{item.userAvatar}}"></image>
              <view class="activity-avatar-placeholder" wx:else>{{item.userName[0]}}</view>
              <view class="activity-user-info">
                <view class="activity-username">{{item.userName}}</view>
                <view class="activity-time">{{item.timeFormatted}}</view>
              </view>
            </view>
            <view class="activity-content">{{item.content}}</view>
            <view class="activity-images" wx:if="{{item.images && item.images.length > 0}}">
              <image
                class="activity-image"
                wx:for="{{item.images}}"
                wx:for-item="image"
                wx:key="*this"
                src="{{image}}"
                mode="aspectFill"
                bindtap="previewImage"
                data-urls="{{item.images}}"
                data-current="{{image}}"
              ></image>
            </view>
            <view class="activity-actions">
              <view class="activity-like {{item.hasLiked ? 'liked' : ''}}" bindtap="toggleLike" data-id="{{item.id}}">
                <text class="like-icon">👍</text>
                <text class="like-count">{{item.likeCount}}</text>
              </view>
              <view class="activity-comment" bindtap="showCommentInput" data-id="{{item.id}}">
                <text class="comment-icon">💬</text>
                <text class="comment-count">{{item.commentCount}}</text>
              </view>
            </view>
            <!-- 评论列表 -->
            <view class="comment-list" wx:if="{{item.comments && item.comments.length > 0}}">
              <view class="comment-item" wx:for="{{item.comments}}" wx:for-item="comment" wx:key="id">
                <text class="comment-username">{{comment.userName}}：</text>
                <text class="comment-content">{{comment.content}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 发布动态按钮 -->
        <view class="publish-activity" bindtap="showActivityModal">
          <text>发布动态</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 发布公告弹窗 -->
  <view class="modal-mask" wx:if="{{showAnnouncementModal}}" bindtap="hideAnnouncementModal"></view>
  <view class="modal-container" wx:if="{{showAnnouncementModal}}">
    <view class="modal-header">
      <text>发布公告</text>
      <view class="modal-close" bindtap="hideAnnouncementModal">×</view>
    </view>
    <view class="modal-content">
      <input
        class="modal-input"
        placeholder="公告标题"
        value="{{announcementTitle}}"
        bindinput="onAnnouncementTitleInput"
      />
      <textarea
        class="modal-textarea"
        placeholder="公告内容"
        value="{{announcementContent}}"
        bindinput="onAnnouncementContentInput"
      ></textarea>
    </view>
    <view class="modal-footer">
      <button
        class="modal-button cancel"
        bindtap="hideAnnouncementModal"
      >取消</button>
      <button
        class="modal-button confirm"
        bindtap="publishAnnouncement"
        disabled="{{!announcementTitle || !announcementContent}}"
      >发布</button>
    </view>
  </view>

  <!-- 发布动态弹窗 -->
  <view class="modal-mask" wx:if="{{showActivityModal}}" bindtap="hideActivityModal"></view>
  <view class="modal-container" wx:if="{{showActivityModal}}">
    <view class="modal-header">
      <text>发布动态</text>
      <view class="modal-close" bindtap="hideActivityModal">×</view>
    </view>
    <view class="modal-content">
      <textarea
        class="modal-textarea"
        placeholder="分享你的动态..."
        value="{{activityContent}}"
        bindinput="onActivityContentInput"
      ></textarea>
    </view>
    <view class="modal-footer">
      <button
        class="modal-button cancel"
        bindtap="hideActivityModal"
      >取消</button>
      <button
        class="modal-button confirm"
        bindtap="publishActivity"
        disabled="{{!activityContent}}"
      >发布</button>
    </view>
  </view>

  <!-- 评论输入框 -->
  <view class="comment-input-container" wx:if="{{showCommentInput}}">
    <input
      class="comment-input"
      placeholder="发表评论..."
      value="{{commentContent}}"
      bindinput="onCommentContentInput"
      focus="{{showCommentInput}}"
      bindblur="hideCommentInput"
    />
    <button
      class="comment-submit"
      bindtap="submitComment"
      disabled="{{!commentContent}}"
    >发送</button>
  </view>
</view>