/* pages/venue-detail/venue-detail.wxss */
page {
  background-color: #f5f5f5;
}

.venue-detail-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.venue-scroll-view {
  flex: 1;
  height: calc(100vh - 90rpx - env(safe-area-inset-top));
  margin-top: 0;
  padding-top: 0;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 导航栏样式 */
.nav-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
  box-sizing: content-box;
}

.nav-back {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 图片轮播样式 */
.gallery-container {
  position: relative;
  width: 100%;
  height: 550rpx;
  margin-top: 0;
  overflow: visible;
  margin-bottom: 20rpx;
}

.gallery-swiper {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

/* 场馆类型标签样式 */
.venue-categories {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  z-index: 999;
  height: 40rpx; /* 固定高度 */
}

.categories-container {
  display: flex;
  flex-direction: row;
  flex: 1;
  z-index: 1000;
  overflow: scroll;
  white-space: nowrap;
  width: 80%;
  height: 40rpx;
}

.category-tag {
  display: inline-block;
  font-size: 22rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6rpx 0;
  border-radius: 6rpx;
  margin-right: 16rpx;
  white-space: nowrap;
  z-index: 1000;
  text-align: center;
  box-sizing: border-box;
}

.image-counter {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  margin-left: 10rpx;
  white-space: nowrap;
  z-index: 1000;
  min-width: 50rpx;
  text-align: center;
  height: 30rpx;
  line-height: 30rpx;
}

/* 场馆名称和基本信息样式 */
.venue-header {
  padding: 20rpx;
  background-color: white;
  margin-bottom: 20rpx;
  position: relative;
}

.venue-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.venue-basic-info {
  font-size: 28rpx;
  color: #666;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
}

.info-content {
  flex: 1;
  color: #333;
  word-break: break-all;
}

.venue-address {
  position: relative;
}

.address-nav-hint {
  margin-left: 10rpx;
  color: #E74C3C;
  font-size: 24rpx;
}

.address-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.venue-distance-text {
  color: #666;
  font-size: 24rpx;
  margin-left: auto;
  white-space: nowrap;
}

.venue-contact {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.contact-text {
  color: #333;
}

/* 通用区块样式 */
.section-container {
  padding: 20rpx;
  background-color: white;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  position: relative;
  padding-left: 20rpx;
  margin-bottom: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #E74C3C;
  border-radius: 4rpx;
}

/* 场馆介绍样式 */
.venue-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
  text-align: justify;
}

.venue-area {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.venue-sports {
  font-size: 28rpx;
  color: #666;
}

.sports-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.sport-tag {
  font-size: 24rpx;
  background-color: #f5f5f5;
  color: #333;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
  border: 1rpx solid #eee;
}

/* 公告信息样式 */
.announcement-list {
  margin-bottom: 10rpx;
}

.announcement-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.announcement-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  color: #F5A623;
}

.announcement-content {
  flex: 1;
}

.announcement-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.announcement-date {
  font-size: 24rpx;
  color: #999;
}

.announcement-arrow {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 场馆单馆样式 */
.sub-venues-list {
  margin-bottom: 10rpx;
}

.sub-venue-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.sub-venue-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.sub-venue-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.sub-venue-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.sub-venue-open-time {
  font-size: 24rpx;
  color: #666;
}

.sub-venue-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  background-color: #E74C3C;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  height: 40rpx;
  margin-top: 40rpx;
}

/* 更多链接样式 */
.more-link {
  text-align: right;
  font-size: 28rpx;
  color: #E74C3C;
}



/* 场地预定样式 */
.booking-container {
  width: 100%;
  padding-left: 0;
  box-sizing: border-box;
}

/* 场地预定卡片样式 - 横向滑动 */
.booking-scroll-wrapper {
  width: 100%;
  height: 180rpx;
  overflow: hidden;
  position: relative;
}
.booking-scroll {
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
  overflow: -moz-scrollbars-none !important; /* Firefox */
  overflow: hidden !important;
}

.booking-scroll::-webkit-scrollbar,
.booking-scroll::-webkit-scrollbar-thumb,
.booking-scroll::-webkit-scrollbar-track,
.booking-scroll::-webkit-scrollbar-button,
.booking-scroll::-webkit-scrollbar-track-piece,
.booking-scroll::-webkit-scrollbar-corner,
.booking-scroll::-webkit-resizer {
  display: none !important; /* Chrome, Safari, Opera */
  width: 0 !important;
  height: 0 !important;
  background-color: transparent !important;
}

.booking-cards-container {
  display: flex;
  flex-direction: row;
  padding: 10rpx 0;
  width: fit-content;
}

.first-card {
  margin-left: 20rpx;
}

.booking-card {
  width: 300rpx;
  height: 160rpx;
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 0;
  margin-right: 20rpx;
}

.booking-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.booking-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.5) 100%);
}

.booking-card-label {
  position: absolute;
  left: 50%;
  bottom: 20rpx;
  transform: translateX(-50%);
  background-color: #E74C3C;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #ffffff;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
  white-space: nowrap;
}

.booking-card-arrow {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
  margin-left: 4rpx;
}
