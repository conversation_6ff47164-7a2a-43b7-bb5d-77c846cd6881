/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100rpx; /* 增加高度 */
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between; /* 两端对齐 */
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

/* 返回按钮样式 */
.nav-back {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
}

/* 占位元素，用于平衡导航栏布局 */
.nav-placeholder {
  width: 80rpx;
  height: 80rpx;
}

.nav-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto; /* 确保整体居中 */
  text-align: center; /* 文本居中 */
}

.nav-logo {
  height: 60rpx;
  width: auto;
  margin-right: 0 rpx; /* 稍微增加间距 */
  vertical-align: middle; /* 确保垂直对齐 */
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

/* 搜索框样式 */
.search-box {
  position: absolute;
  top: calc(120rpx + env(safe-area-inset-top)); /* 调整：导航栏高度 + 安全区域 + 额外间距 */
  left: 30rpx;
  right: 30rpx;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 40rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-input {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.search-input icon {
  margin-right: 20rpx;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
  font-weight: bold;
}

/* 场地列表样式 */
.venue-list-container {
  position: absolute;
  bottom: 00rpx;
  left: 0;
  right: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom); /* 适配iPhone底部安全区域 */
}

.list-toggle {
  position: absolute;
  left: 30rpx; /* 改为左侧 */
  bottom: 530rpx; /* 与右侧定位按钮对齐 - 调整位置 */
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 40rpx;
  padding: 15rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 11;
}

.list-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/></svg>');
  background-size: 32rpx 32rpx;
  background-position: center;
  background-repeat: no-repeat;
}



.venue-swiper {
  height: 320rpx; /* 固定高度确保显示 */
  width: 100%;
  padding: 0; /* 移除内边距，使用margin控制间距 */
  box-sizing: border-box;
}

.venue-swiper-item {
  display: flex;
  justify-content: center; /* 居中对齐 */
  align-items: center;
  height: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;
}

.venue-card {
  width: 480rpx; /* 减小卡片宽度 */
  height: 280rpx;
  background-color: white;
  border-radius: 20rpx;
  margin: 0; /* 移除外边距 */
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.venue-card.active {
  transform: translateY(-10rpx); /* 轻微上浮效果 */
  box-shadow: 0 8px 16px rgba(231, 76, 60, 0.2); /* 红色阴影效果 */
  transition: all 0.3s ease;
}

.venue-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 黑色渐变蒙版 */
.venue-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 30%, rgba(0,0,0,0.7) 100%);
  z-index: 1;
}

.venue-info {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 2;
  color: white;
  width: calc(100% - 40rpx); /* Ensure proper width calculation */
  box-sizing: border-box; /* Include padding in width calculation */
}

.venue-name {
  font-size: 32rpx;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 10rpx;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  max-width: 100%; /* Ensure text doesn't overflow */
  display: block; /* Force block display */
}

.venue-tags {
  display: flex;
  flex-wrap: wrap;
  width: 100%; /* Ensure full width */
  overflow: hidden; /* Prevent overflow */
}

.venue-tag {
  font-size: 22rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

/* 运动类型标签样式 */
.sport-tag {
  background-color: rgba(231, 76, 60, 0.7); /* 工会红色半透明 */
}

/* 距离显示 */
.venue-distance {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 30rpx;
  padding: 6rpx 16rpx;
  display: flex;
  align-items: center;
  z-index: 2;
}

.venue-distance text {
  font-size: 22rpx;
  color: white;
  margin-left: 6rpx;
}

/* 定位按钮 - 特殊样式区分其他入口按钮 */
.location-button {
  position: absolute;
  right: 55rpx; /* 调整右侧位置，使其与上面按钮居中对齐 */
  bottom: 470rpx; /* 与左侧地图海报按钮对齐 */
  width: 60rpx; /* 进一步缩小按钮 */
  height: 60rpx; /* 进一步缩小按钮 */
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影 */
  z-index: 10;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
}

/* 定位图标样式 */
.icon-location {
  width: 30rpx;
  height: 30rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3c-.46-4.17-3.77-7.48-7.94-7.94V1h-2v2.06C6.83 3.52 3.52 6.83 3.06 11H1v2h2.06c.46 4.17 3.77 7.48 7.94 7.94V23h2v-2.06c4.17-.46 7.48-3.77 7.94-7.94H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/></svg>');
  background-size: 30rpx 30rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 认证按钮 */
.auth-button {
  position: absolute;
  right: 30rpx;
  bottom: 950rpx; /* 位于订单按钮上方 - 调整间距 */
  width: 110rpx;
  height: 110rpx;
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.auth-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

/* 认证图标样式 */
.icon-auth {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/></svg>');
  background-size: 36rpx 36rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 我的订单按钮 */
.my-orders-button {
  position: absolute;
  right: 30rpx;
  bottom: 830rpx; /* 位于社团按钮上方 - 调整间距 */
  width: 110rpx;
  height: 110rpx;
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.my-orders-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

/* 文档图标样式 */
.icon-document {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
  background-size: 36rpx 36rpx;
  background-position: center;
  background-repeat: no-repeat;
}




/* 运动社团按钮 */
.club-button {
  position: absolute;
  right: 30rpx;
  bottom: 710rpx; /* 位于我的按钮上方 - 调整间距 */
  width: 110rpx;
  height: 110rpx;
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.club-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

/* 社团图标样式 */
.icon-club {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>');
  background-size: 36rpx 36rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 我的按钮 */
.my-button {
  position: absolute;
  right: 30rpx;
  bottom: 570rpx; /* 位于定位按钮上方 - 调整间距以适应更小的定位按钮 */
  width: 110rpx;
  height: 110rpx;
  background-color: #E74C3C; /* 工会红色 */
  color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.my-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

/* 我的图标样式 */
.icon-my {
  width: 36rpx;
  height: 36rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 36rpx 36rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 按钮标签样式 */
.button-label {
  font-size: 22rpx;
  color: white;
  margin-top: 8rpx;
  font-weight: 500;
}