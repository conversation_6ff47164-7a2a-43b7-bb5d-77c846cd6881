/* pages/club-detail/club-detail.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.club-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-text {
  font-size: 30rpx;
  color: #999;
}

/* 社团信息 */
.club-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.club-header {
  padding: 30rpx;
  background-color: white;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.club-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.club-logo-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
}

.club-basic-info {
  flex: 1;
}

.club-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.club-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.management-icon-small {
  font-size: 36rpx;
  padding: 8rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.club-org {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.club-sports {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10rpx;
}

.sport-tag {
  font-size: 22rpx;
  background-color: #f5f5f5;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
}

.club-member-count {
  font-size: 24rpx;
  color: #999;
}

.club-description {
  padding: 20rpx 30rpx;
  background-color: white;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 选项卡 */
.tab-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #E74C3C;
  font-weight: bold;
}

.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #E74C3C;
  border-radius: 2rpx;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

/* 成员列表 */
.member-list {
  padding: 20rpx 30rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.member-item:last-child {
  border-bottom: none;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.member-avatar-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
}

.member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.member-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 10rpx;
}

.member-role-container {
  display: flex;
  flex-wrap: wrap;
}

.member-role {
  font-size: 22rpx;
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  display: inline-block;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

.member-role.admin {
  color: #2980b9;
  background-color: rgba(41, 128, 185, 0.1);
}

.member-status {
  font-size: 22rpx;
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.member-join-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
}

.member-actions {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
  max-width: 240rpx;
}

.action-btn {
  font-size: 22rpx;
  padding: 0 16rpx;
  height: 50rpx;
  line-height: 50rpx;
  margin: 0 0 10rpx 10rpx;
  border-radius: 6rpx;
  min-width: 110rpx;
  text-align: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  border: none !important;
}

.action-btn::after {
  border: none;
}

.set-admin {
  background-color: #2980b9;
  color: white;
}

.remove-admin {
  background-color: #7f8c8d;
  color: white;
}

.remove-member {
  background-color: #e74c3c;
  color: white;
}

.pending-members {
  margin-top: 30rpx;
  padding-top: 10rpx;
}

.pending-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 4rpx solid #E74C3C;
  display: flex;
  align-items: center;
}

.pending-title::before {
  content: "🔔";
  margin-right: 8rpx;
  font-size: 24rpx;
}

.member-item.pending {
  background-color: #fff9f0;
  border-bottom: 1rpx solid #eee;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
}

.member-item.pending:last-child {
  border-bottom: none;
}

.pending-badge {
  width: 40rpx;
  background-color: #f39c12;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  align-self: stretch;
}

.pending-badge-text {
  color: white;
  font-size: 24rpx;
  writing-mode: vertical-lr;
  text-orientation: upright;
  letter-spacing: -8rpx;
  font-weight: bold;
  line-height: 1.2;
}

.approval-actions {
  display: flex;
  flex-direction: column;
  margin-left: auto;
}

.approve-btn, .reject-btn {
  font-size: 24rpx;
  padding: 0;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 0;
  box-shadow: none;
  border: none !important;
  width: 150rpx;
  text-align: center;
}

.approve-btn {
  background-color: #E74C3C;
  color: white;
  margin-bottom: 10rpx;
}

.reject-btn {
  background-color: #f5f5f5;
  color: #666;
}

.approve-btn::after, .reject-btn::after {
  border: none;
}

/* 公告列表 */
.announcement-list {
  padding: 20rpx 30rpx;
}

.announcement-item {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.announcement-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.announcement-time {
  font-size: 24rpx;
  color: #999;
}

.announcement-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.publish-announcement {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 200rpx;
  height: 80rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(231, 76, 60, 0.3);
  z-index: 10;
}

/* 动态列表 */
.activity-list {
  padding: 20rpx 30rpx;
}

.activity-item {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.activity-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.activity-avatar-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 15rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
}

.activity-user-info {
  flex: 1;
}

.activity-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

.activity-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.activity-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}

.activity-image {
  width: 200rpx;
  height: 200rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

.activity-actions {
  display: flex;
  border-top: 1rpx solid #eee;
  padding-top: 15rpx;
}

.activity-like, .activity-comment {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 26rpx;
  color: #666;
}

.activity-like.liked {
  color: #E74C3C;
}

.like-icon, .comment-icon {
  margin-right: 6rpx;
  font-size: 30rpx;
}

.comment-list {
  margin-top: 15rpx;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.comment-item {
  font-size: 26rpx;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.comment-username {
  color: #333;
  font-weight: bold;
}

.comment-content {
  color: #666;
}

.publish-activity {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 200rpx;
  height: 80rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(231, 76, 60, 0.3);
  z-index: 10;
}

/* 空状态提示 */
.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #999;
  font-size: 28rpx;
}

/* 弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}

.modal-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
  z-index: 101;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.modal-header text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.modal-content {
  padding: 30rpx;
}

.modal-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.modal-textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.modal-footer {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #eee;
}

.modal-button {
  width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  margin: 0;
  margin-left: 20rpx;
}

.modal-button.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-button.confirm {
  background-color: #E74C3C;
  color: white;
}

/* 评论输入框 */
.comment-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

.comment-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.comment-submit {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 28rpx;
  padding: 0;
  margin: 0;
  border-radius: 35rpx;
}