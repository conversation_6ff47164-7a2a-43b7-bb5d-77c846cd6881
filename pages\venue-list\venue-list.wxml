<!--pages/venue-list/venue-list.wxml-->
<view class="venue-list-container" bindtap="onTapBackground">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">场地列表</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-box">
    <view class="search-input-container">
      <icon type="search" size="14" color="#999"></icon>
      <input
        class="search-input"
        placeholder="搜索相关场地名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="searchVenues"
        catchtap="stopPropagation"
      />
      <view class="search-clear" catchtap="clearSearch" wx:if="{{searchKeyword}}">×</view>
    </view>
    <view class="map-btn" catchtap="navigateToMap">
      <image src="/images/map-icon.svg" mode="aspectFit" class="map-icon"></image>
      <text>地图</text>
    </view>
  </view>

  <!-- 筛选栏和下拉菜单容器 -->
  <view class="filter-section">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" catchtap="toggleSortDropdown">
        <text class="{{showSortDropdown ? 'active' : ''}}">综合排序</text>
        <view class="dropdown-icon {{showSortDropdown ? 'up' : 'down'}}"></view>
      </view>
      <view class="filter-item" catchtap="toggleSportTypeDropdown">
        <text class="{{showSportTypeDropdown ? 'active' : ''}}">运动类型</text>
        <view class="dropdown-icon {{showSportTypeDropdown ? 'up' : 'down'}}"></view>
      </view>
      <view class="filter-item" catchtap="toggleFilterDropdown">
        <text class="{{showFilterDropdown ? 'active' : ''}}">筛选</text>
        <view class="dropdown-icon {{showFilterDropdown ? 'up' : 'down'}}"></view>
      </view>
    </view>

    <!-- 综合排序下拉菜单 -->
    <view class="dropdown-menu sort-dropdown {{showSortDropdown ? 'show' : ''}}"
          wx:if="{{showSortDropdown}}"
          catchtap="stopPropagation">
      <view class="dropdown-item {{selectedSortOption === 'comprehensive' ? 'active' : ''}}"
            catchtap="selectSortOption" data-id="comprehensive">
        综合排序
        <view class="check-icon" wx:if="{{selectedSortOption === 'comprehensive'}}">✓</view>
      </view>
      <view class="dropdown-item {{selectedSortOption === 'distance' ? 'active' : ''}}"
            catchtap="selectSortOption" data-id="distance">
        距离优先
        <view class="check-icon" wx:if="{{selectedSortOption === 'distance'}}">✓</view>
      </view>
      <view class="dropdown-item {{selectedSortOption === 'popularity' ? 'active' : ''}}"
            catchtap="selectSortOption" data-id="popularity">
        人气优先
        <view class="check-icon" wx:if="{{selectedSortOption === 'popularity'}}">✓</view>
      </view>
    </view>

    <!-- 运动类型下拉菜单 -->
    <view class="dropdown-menu sport-dropdown {{showSportTypeDropdown ? 'show' : ''}}"
          wx:if="{{showSportTypeDropdown}}"
          catchtap="stopPropagation">
      <view class="sport-type-list">
        <view class="sport-type-item {{item.selected ? 'active' : ''}}"
              wx:for="{{sportTypes}}"
              wx:key="id"
              catchtap="selectSportType"
              data-id="{{item.id}}">
          {{item.name}}
        </view>
      </view>
    </view>

    <!-- 筛选下拉菜单 -->
    <view class="dropdown-menu filter-dropdown {{showFilterDropdown ? 'show' : ''}}"
          wx:if="{{showFilterDropdown}}"
          catchtap="stopPropagation">
      <view class="venue-type-list">
        <view class="venue-type-item {{item.selected ? 'active' : ''}}"
              wx:for="{{venueTypes}}"
              wx:key="id"
              catchtap="selectVenueType"
              data-id="{{item.id}}">
          {{item.name}}
        </view>
      </view>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 场馆列表 -->
  <scroll-view
    scroll-y="true"
    class="venues-scroll-view"
    wx:if="{{!loading && venues.length > 0}}"
    enable-back-to-top="true"
    enhanced="true"
    show-scrollbar="false">
    <view class="venue-list">
      <view
        class="venue-card"
        wx:for="{{venues}}"
        wx:key="id"
        bindtap="onVenueCardTap"
        data-id="{{item.id}}">
        <image src="{{item.imageUrl}}" mode="aspectFill" class="venue-image"></image>
        <view class="venue-info">
          <view class="venue-name">{{item.name}}</view>
          <view class="venue-type">{{item.type}}</view>
          <view class="venue-distance">{{item.formattedDistance}}</view>

          <!-- 运动类型标签 -->
          <view class="venue-sports">
            <view class="sport-tag" wx:for="{{item.sports}}" wx:for-item="sport" wx:key="*this" wx:if="{{index < 3}}">
              {{sport}}
            </view>
            <view class="sport-tag more-tag" wx:if="{{item.sports.length > 3}}">
              +{{item.sports.length - 3}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 无结果提示 -->
  <view class="no-result" wx:if="{{!loading && venues.length === 0}}">
    <icon type="info" size="64" color="#ccc"></icon>
    <text>未找到相关场馆</text>
  </view>


</view>
