<!--pages/venue-detail/venue-detail.wxml-->
<view class="venue-detail-container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <block wx:if="{{!loading && venue}}">
    <!-- 内容区域（可滚动） -->
    <scroll-view scroll-y="true" class="venue-scroll-view">

    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="navigateBack">
        <view class="back-icon">←</view>
      </view>
      <view class="nav-placeholder"></view>
      <view class="nav-placeholder"></view>
    </view>
      <!-- 顶部轮播图 -->
      <view class="gallery-container">
        <swiper
          class="gallery-swiper"
          indicator-dots="{{false}}"
          autoplay="{{true}}"
          interval="{{3000}}"
          duration="{{500}}"
          circular="{{true}}"
          bindchange="onSwiperChange">
          <swiper-item wx:for="{{venue.images}}" wx:key="index" bindtap="previewImage" data-index="{{index}}">
            <image src="{{item}}" mode="aspectFill" class="gallery-image"></image>
          </swiper-item>
        </swiper>

        <!-- 使用cover-view确保在轮播图上方显示 -->
        <cover-view class="venue-categories">
          <cover-view class="categories-container">
            <cover-view class="category-tag" wx:for="{{venue.categories}}" wx:key="index" style="width: auto; padding-left: 12rpx; padding-right: 12rpx;">{{item}}</cover-view>
          </cover-view>
          <cover-view class="image-counter">{{currentImageIndex + 1}}/{{venue.images.length}}</cover-view>
        </cover-view>
      </view>

      <!-- 场馆名称和基本信息 -->
      <view class="venue-header">
        <view class="venue-name">{{venue.name}}</view>
        <view class="venue-basic-info">
          <view class="info-row">
            <view class="info-label">开放时间：</view>
            <view class="info-content">{{venue.openTime}}</view>
          </view>
          <view class="info-row" bindtap="openLocation">
            <view class="info-label">场地地址：</view>
            <view class="info-content">
              <view class="address-container">
                <text>{{venue.address}}</text>
                <text class="address-nav-hint">></text>
                <text class="venue-distance-text">📍 距离您{{venue.distance}}</text>
              </view>
            </view>
          </view>
          <view class="info-row" bindtap="makePhoneCall">
            <view class="info-label">场地电话：</view>
            <view class="info-content">{{venue.contact}}</view>
          </view>
        </view>
      </view>

      <!-- 场馆介绍 -->
      <view class="section-container">
        <view class="section-title">场地介绍</view>
        <view class="venue-description">{{venue.description}}</view>
        <view class="venue-area">场地面积：<text class="info-value">{{venue.area}}</text></view>
        <view class="venue-sports">
          <text>运动项目：</text>
          <view class="sports-tags">
            <view class="sport-tag" wx:for="{{venue.sports}}" wx:key="index">{{item}}</view>
          </view>
        </view>
      </view>

      <!-- 公告信息 -->
      <view class="section-container" wx:if="{{venue.announcements && venue.announcements.length > 0}}">
        <view class="section-header">
          <view class="section-title">公告信息</view>
          <view class="more-link" wx:if="{{venue.announcements.length > 2}}">
            <text>更多 ></text>
          </view>
        </view>
        <view class="announcement-list">
          <view class="announcement-item"
                wx:for="{{venue.announcements}}"
                wx:key="id"
                bindtap="viewAnnouncementDetail"
                data-id="{{item.id}}">
            <view class="announcement-icon">📢</view>
            <view class="announcement-content">
              <view class="announcement-title">{{item.title}}</view>
              <view class="announcement-date">{{item.date}}</view>
            </view>
            <view class="announcement-arrow">></view>
          </view>
        </view>
      </view>

      <!-- 场地预定 -->
      <view class="section-container">
        <view class="section-title">场地预定</view>
        <view class="booking-container">
          <!-- 场地预定卡片 - 横向滑动 -->
          <view class="booking-scroll-wrapper">
            <scroll-view class="booking-scroll"
                   scroll-x="true"
                   enhanced="true"
                   show-scrollbar="{{false}}"
                   enable-flex="true"
                   style="overflow: hidden;">
            <view class="booking-cards-container">
              <view class="booking-card first-card" bindtap="selectSportType" data-type="羽毛球">
                <image src="https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg" mode="aspectFill" class="booking-card-image"></image>
                <view class="booking-card-overlay"></view>
                <view class="booking-card-label">
                  羽毛球 <text class="booking-card-arrow">›</text>
                </view>
              </view>
              <view class="booking-card" bindtap="selectSportType" data-type="乒乓球">
                <image src="https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg" mode="aspectFill" class="booking-card-image"></image>
                <view class="booking-card-overlay"></view>
                <view class="booking-card-label">
                  乒乓球 <text class="booking-card-arrow">›</text>
                </view>
              </view>
              <view class="booking-card" bindtap="selectSportType" data-type="网球">
                <image src="https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg" mode="aspectFill" class="booking-card-image"></image>
                <view class="booking-card-overlay"></view>
                <view class="booking-card-label">
                  网球 <text class="booking-card-arrow">›</text>
                </view>
              </view>
              <view class="booking-card" bindtap="selectSportType" data-type="足球">
                <image src="https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg" mode="aspectFill" class="booking-card-image"></image>
                <view class="booking-card-overlay"></view>
                <view class="booking-card-label">
                  足球 <text class="booking-card-arrow">›</text>
                </view>
              </view>
            </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <!-- 场馆单馆 -->
      <view class="section-container">
        <view class="section-title">场地单馆</view>
        <view class="sub-venues-list">
          <view class="sub-venue-item"
                wx:for="{{venue.subVenues}}"
                wx:key="id"
                bindtap="viewSubVenueDetail"
                data-id="{{item.id}}">
            <image src="{{item.imageUrl}}" mode="aspectFill" class="sub-venue-image"></image>
            <view class="sub-venue-info">
              <view class="sub-venue-name">{{item.name}}</view>
              <view class="sub-venue-open-time">营业时间：{{item.openTime}}</view>
            </view>
            <view class="sub-venue-arrow">查看</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </block>
</view>
