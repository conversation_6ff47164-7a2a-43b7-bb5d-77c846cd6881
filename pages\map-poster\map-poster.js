// pages/map-poster/map-poster.js
import * as api from '../../utils/api';

Page({
  data: {
    venues: [],
    loading: true,
    canvasWidth: 0,
    canvasHeight: 0,
    // 地图边界坐标 (从GeoJSON文件中提取)
    mapBounds: {
      minLat: 30.689454534789707, // 最小纬度
      maxLat: 30.790967059193086, // 最大纬度
      minLng: 120.81379006912097, // 最小经度
      maxLng: 120.97273406871989 // 最大经度
    },
    // 画布边距
    padding: {
      top: 40,
      right: 40,
      bottom: 40,
      left: 40
    },
    // GeoJSON数据
    geoJsonLoaded: false,
    // 聚合距离阈值（像素）- 增加聚合范围以确保更一致的聚合效果
    clusterDistance: 40,
    // 聚合后的点位
    clusters: [],
    // 当前选中的聚合点索引
    selectedClusterIndex: -1,
    // 当前选中的场馆ID列表
    selectedVenueIds: [],
    // 滚动到的场馆ID
    scrollToVenueId: null,
    // 过滤后的场馆列表（当前选中气泡的场馆）
    filteredVenues: [],
    // 是否显示所有场馆
    showAllVenues: true
  },

  onLoad: function() {
    // 获取系统信息以确定画布大小
    const systemInfo = wx.getSystemInfoSync();
    // 将画布宽度设为窗口宽度的90%，使地图整体缩小
    const canvasWidth = systemInfo.windowWidth * 0.9;
    // 保持地图图片的宽高比为4:3，确保地图不变形
    const canvasHeight = canvasWidth * 0.75; // 保持4:3的宽高比

    this.setData({
      canvasWidth,
      canvasHeight,
      // 增加顶部内边距，为标题留出空间
      padding: {
        top: 60, // 增加顶部内边距
        right: 40,
        bottom: 40,
        left: 40
      }
    });

    // 加载GeoJSON数据
    this.loadGeoJsonData();

    // 加载场馆数据
    this.loadVenueData();
  },

  // 页面显示时执行
  onShow: function() {
    // 如果已经加载了场馆数据，但还没有选中任何气泡，则自动选择最近的气泡
    if (this.data.venues.length > 0 && this.data.selectedClusterIndex === -1) {
      this.selectNearestCluster();
    }
  },

  // 选择距离最近的气泡
  selectNearestCluster: function() {
    const { clusters, selectedClusterIndex } = this.data;
    if (!clusters || clusters.length === 0) return;

    // 如果已经选择了气泡，不再自动选择
    if (selectedClusterIndex !== -1) return;

    // 获取用户当前位置
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const userLat = res.latitude;
        const userLng = res.longitude;

        console.log('用户位置:', userLat, userLng);

        // 找到距离用户最近的气泡
        let nearestClusterIndex = -1;
        let minDistance = Number.MAX_VALUE;

        clusters.forEach((cluster, index) => {
          // 使用第一个场馆的位置计算距离
          if (cluster.venues && cluster.venues.length > 0) {
            const venue = cluster.venues[0];
            if (venue.latitude && venue.longitude) {
              // 计算地理距离
              const distance = this.calculateDistance(
                userLat, userLng,
                venue.latitude, venue.longitude
              );

              console.log(`气泡 ${index} 距离: ${distance}米`);

              if (distance < minDistance) {
                minDistance = distance;
                nearestClusterIndex = index;
              }
            }
          }
        });

        console.log('最近的气泡索引:', nearestClusterIndex, '距离:', minDistance, '米');

        // 如果找到了最近的气泡，自动选择它
        if (nearestClusterIndex !== -1) {
          this.selectCluster(nearestClusterIndex);

          // 显示提示
          wx.showToast({
            title: '已自动选择最近场馆',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        // 如果无法获取位置，默认选择第一个气泡
        if (clusters.length > 0) {
          this.selectCluster(0);

          // 显示提示
          wx.showToast({
            title: '已自动选择场馆',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 计算两点之间的地理距离（米）
  calculateDistance: function(lat1, lng1, lat2, lng2) {
    const R = 6371000; // 地球半径，单位米
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return distance;
  },

  // 角度转弧度
  deg2rad: function(deg) {
    return deg * (Math.PI/180);
  },

  // 加载GeoJSON数据
  loadGeoJsonData: function() {
    // 由于微信小程序限制，我们直接使用预先计算好的边界值
    // 这些值是从GeoJSON文件中提取的
    const mapBounds = {
      // 根据您提供的GeoJSON文件计算的边界
      minLat: 30.689454534789707,
      maxLat: 30.790967059193086,
      minLng: 120.81379006912097,
      maxLng: 120.97273406871989
    };

    // 根据实际地图图片调整边界
    // 这些值可能需要根据实际情况进行微调
    const adjustedMapBounds = {
      minLat: 30.689454534789707,
      maxLat: 30.790967059193086,
      minLng: 120.81379006912097,
      maxLng: 120.97273406871989
    };

    this.setData({
      mapBounds: adjustedMapBounds,
      geoJsonLoaded: true
    });
  },

  // 加载场馆数据
  loadVenueData: function() {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    // 从API获取数据
    api.getVenues()
      .then(res => {
        wx.hideLoading();
        // 使用API返回的数据
        const venuesData = res.data || [];

        // 计算地图边界
        let minLat = 90, maxLat = -90, minLng = 180, maxLng = -180;

        venuesData.forEach(venue => {
          if (venue.latitude < minLat) minLat = venue.latitude;
          if (venue.latitude > maxLat) maxLat = venue.latitude;
          if (venue.longitude < minLng) minLng = venue.longitude;
          if (venue.longitude > maxLng) maxLng = venue.longitude;
        });

        // 添加边界缓冲区
        const latBuffer = (maxLat - minLat) * 0.1;
        const lngBuffer = (maxLng - minLng) * 0.1;

        minLat -= latBuffer;
        maxLat += latBuffer;
        minLng -= lngBuffer;
        maxLng += lngBuffer;

        that.setData({
          venues: venuesData,
          mapBounds: {
            minLat,
            maxLat,
            minLng,
            maxLng
          },
          loading: false
        }, () => {
          // 数据加载完成后计算聚合点位
          that.calculateClusters();
          // 绘制地图
          that.drawMapWithVenues();

          // 数据加载完成后，自动选择最近的气泡
          setTimeout(() => {
            that.selectNearestCluster();
          }, 500); // 延迟500ms，确保地图和气泡已经绘制完成
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error('加载场馆数据失败', err);
        wx.showToast({
          title: '加载场馆数据失败',
          icon: 'none'
        });
      });
  },

  // 计算聚合点位
  calculateClusters: function() {
    const { venues, mapBounds, padding, canvasWidth, canvasHeight, clusterDistance } = this.data;
    const drawAreaWidth = canvasWidth - padding.left - padding.right;
    const drawAreaHeight = canvasHeight - padding.top - padding.bottom;

    // 先将场馆转换为画布上的坐标点
    const points = venues.map(venue => {
      const x = this.longitudeToX(venue.longitude, mapBounds.minLng, mapBounds.maxLng, padding.left, drawAreaWidth);
      const y = this.latitudeToY(venue.latitude, mapBounds.minLat, mapBounds.maxLat, padding.top, drawAreaHeight);
      return {
        x,
        y,
        venue
      };
    });

    // 改进的聚合算法 - 使用分层聚类方法
    const clusters = [];

    // 如果没有点，直接返回空数组
    if (points.length === 0) {
      this.setData({ clusters });
      return;
    }

    // 计算所有点之间的距离矩阵
    const distanceMatrix = [];
    for (let i = 0; i < points.length; i++) {
      distanceMatrix[i] = [];
      for (let j = 0; j < points.length; j++) {
        if (i === j) {
          distanceMatrix[i][j] = Infinity; // 自己到自己的距离设为无穷大，避免自聚合
        } else {
          distanceMatrix[i][j] = Math.sqrt(
            Math.pow(points[i].x - points[j].x, 2) +
            Math.pow(points[i].y - points[j].y, 2)
          );
        }
      }
    }

    // 初始化每个点为一个独立的聚类
    const clusterSets = points.map((point, index) => ({
      indices: [index],
      x: point.x,
      y: point.y,
      venues: [point.venue],
      count: 1
    }));

    // 当还有多个聚类且最小距离小于阈值时，继续合并
    while (clusterSets.length > 1) {
      // 找到距离最近的两个聚类
      let minDistance = Infinity;
      let mergeI = -1;
      let mergeJ = -1;

      for (let i = 0; i < clusterSets.length; i++) {
        for (let j = i + 1; j < clusterSets.length; j++) {
          // 计算两个聚类之间的最小距离
          let minClusterDistance = Infinity;

          // 遍历第一个聚类中的所有点
          for (const indexI of clusterSets[i].indices) {
            // 遍历第二个聚类中的所有点
            for (const indexJ of clusterSets[j].indices) {
              const dist = distanceMatrix[indexI][indexJ];
              if (dist < minClusterDistance) {
                minClusterDistance = dist;
              }
            }
          }

          // 如果找到更近的聚类对，更新记录
          if (minClusterDistance < minDistance) {
            minDistance = minClusterDistance;
            mergeI = i;
            mergeJ = j;
          }
        }
      }

      // 如果最小距离大于阈值，停止合并
      if (minDistance > clusterDistance) {
        break;
      }

      // 合并这两个聚类
      const clusterI = clusterSets[mergeI];
      const clusterJ = clusterSets[mergeJ];

      // 计算合并后的中心点（加权平均）
      const totalCount = clusterI.count + clusterJ.count;
      const newX = (clusterI.x * clusterI.count + clusterJ.x * clusterJ.count) / totalCount;
      const newY = (clusterI.y * clusterI.count + clusterJ.y * clusterJ.count) / totalCount;

      // 创建新的合并聚类
      const newCluster = {
        indices: [...clusterI.indices, ...clusterJ.indices],
        x: newX,
        y: newY,
        venues: [...clusterI.venues, ...clusterJ.venues],
        count: totalCount
      };

      // 从列表中移除旧聚类，添加新聚类
      clusterSets.splice(Math.max(mergeI, mergeJ), 1);
      clusterSets.splice(Math.min(mergeI, mergeJ), 1);
      clusterSets.push(newCluster);
    }

    // 将最终的聚类集合转换为clusters数组
    clusters.push(...clusterSets.map(clusterSet => ({
      x: clusterSet.x,
      y: clusterSet.y,
      venues: clusterSet.venues,
      count: clusterSet.count
    })));

    this.setData({ clusters });
  },

  // 绘制地图和场馆标记
  drawMapWithVenues: function() {
    // 回退到旧的绘图方式，避免图片模糊和闪烁问题
    this.drawMapWithVenuesLegacy();
  },

  // 绘制聚合点
  drawClusters: function(ctx, clusters, selectedClusterIndex) {
    // 绘制每个聚合点
    clusters.forEach((cluster, index) => {
      // 获取聚合点的位置
      let x = cluster.x;
      let y = cluster.y;

      // 根据第一个场馆ID进行特殊调整（这里可以根据实际情况调整特定场馆的位置）
      if (cluster.venues.length === 1) {
        const venue = cluster.venues[0];
        if (venue.id === 1) { // 万众运动（大桥镇浙江清华长三角研究院店）
          // 调整位置
          const { padding, canvasWidth, canvasHeight } = this.data;
          const drawAreaWidth = canvasWidth - padding.left - padding.right;
          const drawAreaHeight = canvasHeight - padding.top - padding.bottom;
          x = padding.left + drawAreaWidth * 0.2;
          y = padding.top + drawAreaHeight * 0.5;
        } else if (venue.id === 2) { // 万众测试学校 - 校园场地开放
          // 调整位置
          const { padding, canvasWidth, canvasHeight } = this.data;
          const drawAreaWidth = canvasWidth - padding.left - padding.right;
          const drawAreaHeight = canvasHeight - padding.top - padding.bottom;
          x = padding.left + drawAreaWidth * 0.6;
          y = padding.top + drawAreaHeight * 0.2;
        } else if (venue.id === 3) { // 南湖区市民公园
          // 调整位置
          const { padding, canvasWidth, canvasHeight } = this.data;
          const drawAreaWidth = canvasWidth - padding.left - padding.right;
          const drawAreaHeight = canvasHeight - padding.top - padding.bottom;
          x = padding.left + drawAreaWidth * 0.4;
          y = padding.top + drawAreaHeight * 0.3;
        }
      }

      // 保存当前点的位置，用于点击检测
      cluster.x = x;
      cluster.y = y;

      // 判断是否是选中的聚合点
      const isSelected = (index === selectedClusterIndex);

      // 根据聚合点包含的场馆数量决定标记点大小 - 增大气泡尺寸
      let radius = cluster.count === 1 ? 12 : 15 + Math.min(cluster.count, 8);

      // 如果是选中的聚合点，增大半径
      if (isSelected) {
        radius += 5;
      }

      // 绘制标记点 - 使用工会风格的黄色
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = isSelected ? '#FFC107' : '#FFEB3B'; // 选中时使用更深的黄色
      ctx.fill();

      // 添加边框使气泡更加明显
      ctx.lineWidth = isSelected ? 2.5 : 1.5;
      ctx.strokeStyle = '#E74C3C'; // 红色边框
      ctx.stroke();

      // 添加阴影效果
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = isSelected ? 5 : 3;
      ctx.shadowOffsetX = isSelected ? 2 : 1;
      ctx.shadowOffsetY = isSelected ? 2 : 1;

      // 重置阴影效果，避免影响文字
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // 绘制场馆数量 - 增大字体
      ctx.font = `${isSelected ? 18 : (cluster.count === 1 ? 14 : 16)}px sans-serif`;
      ctx.fillStyle = '#333333'; // 深色文字在黄色背景上更清晰
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 始终显示场馆数量
      const text = cluster.count.toString();
      ctx.fillText(text, x, y);
    });
  },

  // 使用旧版Canvas API绘图方法
  drawMapWithVenuesLegacy: function() {
    const that = this;
    const { clusters, canvasWidth, canvasHeight, mapBounds, padding, geoJsonLoaded, selectedClusterIndex } = this.data;

    // 创建画布上下文
    const ctx = wx.createCanvasContext('mapCanvas');

    // 清除画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 绘制渐变背景 - 使用更中性的米色/浅灰色调
    const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight);
    gradient.addColorStop(0, '#f9f9f9');  // 几乎白色起始
    gradient.addColorStop(0.5, '#f5f5f5'); // 浅灰色中间
    gradient.addColorStop(1, '#eeeeee');   // 浅灰色结束
    ctx.setFillStyle(gradient);
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 移除了网格纹理和边框

    // 绘制地图背景 - 保持原始宽高比，确保地图不变形
    // 使用半透明绘制地图，让背景纹理显示出来
    ctx.setGlobalAlpha(0.85);
    ctx.drawImage('/images/daqiao_map.png', 0, 0, canvasWidth, canvasHeight);
    ctx.setGlobalAlpha(1.0);

    // 记录所有气泡的位置，用于调试
    console.log('画布尺寸:', canvasWidth, 'x', canvasHeight);
    console.log('气泡数量:', clusters.length);

    // 将所有气泡位置保存到全局变量，方便调试
    this.bubblePositions = clusters.map((cluster, index) => {
      return {
        index,
        x: cluster.x,
        y: cluster.y,
        count: cluster.count,
        venues: cluster.venues.map(v => v.id)
      };
    });

    // 计算有效绘图区域（考虑边距）
    const drawAreaWidth = canvasWidth - padding.left - padding.right;
    const drawAreaHeight = canvasHeight - padding.top - padding.bottom;

    // 重置阴影效果，避免影响后续绘制
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 绘制每个聚合点
    clusters.forEach((cluster, index) => {
      // 获取聚合点的位置
      let x = cluster.x;
      let y = cluster.y;

      // 根据第一个场馆ID进行特殊调整（这里可以根据实际情况调整特定场馆的位置）
      if (cluster.venues.length === 1) {
        const venue = cluster.venues[0];
        if (venue.id === 1) { // 万众运动（大桥镇浙江清华长三角研究院店）
          // 调整位置
          x = padding.left + drawAreaWidth * 0.2;
          y = padding.top + drawAreaHeight * 0.5;
        } else if (venue.id === 2) { // 万众测试学校 - 校园场地开放
          // 调整位置
          x = padding.left + drawAreaWidth * 0.6;
          y = padding.top + drawAreaHeight * 0.2;
        } else if (venue.id === 3) { // 南湖区市民公园
          // 调整位置
          x = padding.left + drawAreaWidth * 0.4;
          y = padding.top + drawAreaHeight * 0.3;
        }
      }

      // 保存当前点的位置，用于点击检测
      cluster.x = x;
      cluster.y = y;

      // 判断是否是选中的聚合点
      const isSelected = (index === selectedClusterIndex);
      const isSingleVenue = cluster.count === 1;

      // 根据聚合点包含的场馆数量决定标记点大小
      // 单个点时使用更小的气泡，多个点时根据数量增大尺寸
      let radius = isSingleVenue ? 8 : 15 + Math.min(cluster.count, 8);

      // 如果是选中的聚合点，增大半径
      if (isSelected) {
        radius += 5;
      }

      // 添加阴影效果 - 在绘制前设置
      ctx.setShadow(
        isSelected ? 2 : 1,  // shadowOffsetX
        isSelected ? 2 : 1,  // shadowOffsetY
        isSelected ? 5 : 3,  // shadowBlur
        'rgba(0, 0, 0, 0.3)' // shadowColor
      );

      // 绘制标记点 - 使用工会风格的黄色
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, 2 * Math.PI);

      // 使用渐变色填充气泡，让气泡看起来更立体
      const gradient = ctx.createCircularGradient(x, y, radius);
      if (isSelected) {
        // 选中状态使用更深的黄色渐变
        gradient.addColorStop(0, '#FFF176'); // 亮黄色中心
        gradient.addColorStop(0.7, '#FFC107'); // 中间过渡色
        gradient.addColorStop(1, '#FFA000'); // 深黄色边缘
      } else {
        // 未选中状态使用标准黄色渐变
        gradient.addColorStop(0, '#FFF9C4'); // 浅黄色中心
        gradient.addColorStop(0.7, '#FFEB3B'); // 中间过渡色
        gradient.addColorStop(1, '#FDD835'); // 深黄色边缘
      }
      ctx.setFillStyle(gradient);
      ctx.fill();

      // 添加边框使气泡更加明显
      ctx.setLineWidth(isSelected ? 2.5 : 1.5);
      ctx.setStrokeStyle('#E74C3C'); // 红色边框
      ctx.stroke();

      // 重置阴影效果，避免影响文字
      ctx.setShadow(0, 0, 0, 'transparent');

      // 只有当聚合点包含多个场馆时才绘制数字
      if (!isSingleVenue) {
        // 绘制场馆数量 - 增大字体
        ctx.setFontSize(isSelected ? 18 : 16); // 选中时更大字体

        // 使用白色文字，更加醒目且与工会红色主题搭配更好
        ctx.setFillStyle('#FFFFFF');
        ctx.setTextAlign('center');

        // 显示场馆数量
        const text = cluster.count.toString();

        // 为文字添加细微的阴影，增强可读性
        ctx.setShadow(1, 1, 2, 'rgba(0, 0, 0, 0.5)');
        ctx.fillText(text, x, y + 6); // 调整文字位置

        // 再次重置阴影
        ctx.setShadow(0, 0, 0, 'transparent');
      }
    });

    // 图例已移除

    // 绘制到画布
    ctx.draw();
  },

  // 将经度转换为画布X坐标
  longitudeToX: function(longitude, minLng, maxLng, offsetX, width) {
    // 计算经度在范围内的比例
    const proportion = (longitude - minLng) / (maxLng - minLng);
    // 转换为画布坐标
    return offsetX + (proportion * width);
  },

  // 将纬度转换为画布Y坐标
  latitudeToY: function(latitude, minLat, maxLat, offsetY, height) {
    // 计算纬度在范围内的比例（注意纬度是反向的，所以用1减去）
    const proportion = 1 - ((latitude - minLat) / (maxLat - minLat));
    // 转换为画布坐标
    return offsetY + (proportion * height);
  },

  // 调整坐标以适应地图图片
  adjustCoordinates: function(x, y) {
    // 这个函数可以根据实际情况调整坐标
    // 例如，如果地图图片有偏移或缩放，可以在这里进行调整
    return {
      x: x,
      y: y
    };
  },

  // 返回上一页 - 保留但不再使用
  navigateBack: function() {
    wx.navigateBack();
  },

  // 导航到地图页面
  navigateToIndex: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },

  // 处理画布点击事件
  onCanvasTap: function(e) {
    // 防止重复触发
    if (this.tapProcessing) return;
    this.tapProcessing = true;

    setTimeout(() => {
      this.tapProcessing = false;
    }, 300); // 300ms防抖

    const { clusters, canvasWidth, selectedClusterIndex: currentSelectedIndex } = this.data;
    if (clusters.length === 0) return;

    // 获取点击位置 - 兼容不同事件对象格式
    let x, y;
    if (e.touches && e.touches.length > 0) {
      x = e.touches[0].x;
      y = e.touches[0].y;
    } else if (e.changedTouches && e.changedTouches.length > 0) {
      x = e.changedTouches[0].x;
      y = e.changedTouches[0].y;
    } else if (e.detail) {
      x = e.detail.x;
      y = e.detail.y;
    }

    // 如果无法获取坐标，使用默认坐标系统
    if (!x || !y) {
      try {
        const rect = e.currentTarget.getBoundingClientRect();
        if (e.touches && e.touches.length > 0) {
          x = e.touches[0].clientX - rect.left;
          y = e.touches[0].clientY - rect.top;
        } else if (e.changedTouches && e.changedTouches.length > 0) {
          x = e.changedTouches[0].clientX - rect.left;
          y = e.changedTouches[0].clientY - rect.top;
        } else if (e.detail) {
          x = e.detail.clientX - rect.left;
          y = e.detail.clientY - rect.top;
        }
      } catch (err) {
        console.error('获取点击坐标时出错:', err);
      }
    }

    // 如果仍然无法获取坐标，尝试使用固定坐标
    if (!x || !y) {
      // 尝试使用点击事件的clientX和clientY
      if (e.detail && e.detail.clientX && e.detail.clientY) {
        // 估算canvas在页面中的位置
        const canvasTop = 90; // 导航栏高度
        x = e.detail.clientX;
        y = e.detail.clientY - canvasTop;
      } else {
        console.error('无法获取点击坐标');
        this.tapProcessing = false;
        return;
      }
    }

    console.log('点击坐标:', x, y);

    // 查找被点击的聚合点
    let selectedClusterIndex = -1;
    let minDistance = Number.MAX_VALUE;

    clusters.forEach((cluster, index) => {
      const distance = Math.sqrt(
        Math.pow(x - cluster.x, 2) +
        Math.pow(y - cluster.y, 2)
      );

      // 判断点击是否在聚合点的半径范围内
      const isSingleVenue = cluster.count === 1;
      const radius = isSingleVenue ? 8 : 15 + Math.min(cluster.count, 8);

      // 增加点击区域，使点击更容易
      const hitRadius = radius + 50; // 进一步增大点击区域

      console.log(`气泡 ${index}: 位置(${cluster.x}, ${cluster.y}), 距离: ${distance}, 点击范围: ${hitRadius}`);

      if (distance <= hitRadius && distance < minDistance) {
        minDistance = distance;
        selectedClusterIndex = index;
      }
    });

    console.log('选中的气泡索引:', selectedClusterIndex);

    // 如果找到被点击的聚合点
    if (selectedClusterIndex !== -1) {
      // 检查是否点击了已选中的聚合点
      if (selectedClusterIndex === currentSelectedIndex) {
        // 如果是，取消选择
        this.cancelSelection();
      } else {
        // 如果不是，选择新的聚合点
        this.selectCluster(selectedClusterIndex);
      }
    } else {
      // 如果点击区域没有气泡，尝试使用最近的气泡
      // 这是一个备用方案，帮助用户在点击不精确时也能选中气泡
      if (clusters.length > 0 && minDistance < canvasWidth / 3) { // 如果距离最近的气泡不超过画布宽度的1/3
        console.log('使用最近的气泡:', minDistance);
        this.selectCluster(clusters.indexOf(clusters.reduce((prev, curr) => {
          const distPrev = Math.sqrt(Math.pow(x - prev.x, 2) + Math.pow(y - prev.y, 2));
          const distCurr = Math.sqrt(Math.pow(x - curr.x, 2) + Math.pow(y - curr.y, 2));
          return distPrev < distCurr ? prev : curr;
        })));
      }
    }
  },

  // 选择聚合点
  selectCluster: function(clusterIndex) {
    const { clusters } = this.data;
    if (clusterIndex < 0 || clusterIndex >= clusters.length) return;

    const cluster = clusters[clusterIndex];
    const selectedVenueIds = cluster.venues.map(venue => venue.id);

    // 如果有场馆，滚动到第一个场馆
    const scrollToVenueId = selectedVenueIds.length > 0 ? selectedVenueIds[0] : null;

    this.setData({
      selectedClusterIndex: clusterIndex,
      selectedVenueIds: selectedVenueIds,
      scrollToVenueId: scrollToVenueId,
      filteredVenues: cluster.venues,
      showAllVenues: false
    });

    // 重绘地图以显示选中状态
    this.drawMapWithVenues();

    // 添加触觉反馈
    if (wx.vibrateShort) {
      try {
        wx.vibrateShort({
          type: 'light'
        });
      } catch (e) {
        // 忽略振动失败的错误
      }
    }

    // 显示提示
    wx.showToast({
      title: `已选择${cluster.venues.length}个场馆`,
      icon: 'none',
      duration: 1500
    });
  },

  // 显示所有场馆
  showAllVenues: function() {
    this.setData({
      showAllVenues: true,
      selectedClusterIndex: -1,
      selectedVenueIds: []
    });

    // 重绘地图以显示正常状态
    this.drawMapWithVenues();
  },

  // 取消选择
  cancelSelection: function() {
    this.setData({
      showAllVenues: true,
      selectedClusterIndex: -1,
      selectedVenueIds: [],
      filteredVenues: []
    });

    // 重绘地图以显示正常状态
    this.drawMapWithVenues();

    // 添加触觉反馈
    if (wx.vibrateShort) {
      try {
        wx.vibrateShort({
          type: 'light'
        });
      } catch (e) {
        // 忽略振动失败的错误
      }
    }

    // 显示提示
    wx.showToast({
      title: '已取消选择',
      icon: 'none',
      duration: 1500
    });
  },

  // 处理场馆列表项点击事件
  onVenueItemTap: function(e) {
    const venueId = e.currentTarget.dataset.venueId;

    // 跳转到场地详情页
    wx.navigateTo({
      url: `/pages/venue-detail/venue-detail?id=${venueId}`
    });
  },

  // 导航到企事业单位列表页面
  navigateToOrganizations: function() {
    wx.navigateTo({
      url: '/pages/organizations/organizations'
    });
  },

  // 导航到我的订单页面
  navigateToMyOrders: function() {
    wx.navigateTo({
      url: '/pages/my-orders/my-orders'
    });
  },

  // 导航到运动社团页面
  navigateToClubs: function() {
    wx.navigateTo({
      url: '/pages/clubs/clubs'
    });
  },

  // 导航到我的页面
  navigateToMy: function() {
    wx.navigateTo({
      url: '/pages/my/my'
    });
  }
});
