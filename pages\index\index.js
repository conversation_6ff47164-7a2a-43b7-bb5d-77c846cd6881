// index.js
import * as api from '../../utils/api';

Page({
  data: {
    // 地图中心点坐标（默认值，会在onLoad中更新）
    latitude: 31.2304,
    longitude: 121.4737,
    scale: 14,
    markers: [],
    venues: [],
    searchKeyword: '',
    selectedVenueId: null, // 当前选中的场地ID
    currentSwiperIndex: 0, // 当前swiper索引
    swiperChanging: false // 是否正在切换swiper
  },

  onLoad: function() {
    // 获取用户位置
    this.getUserLocation();

    // 加载场馆数据（实际项目中应从服务器获取）
    this.loadVenueData();
  },

  // 获取用户当前位置
  getUserLocation: function() {
    const that = this;

    // 先检查位置权限
    wx.getSetting({
      success: (res) => {
        // 如果未授权，先请求授权
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              that.getLocationInfo();
            },
            fail: () => {
              // 用户拒绝授权，引导用户开启授权
              wx.showModal({
                title: '提示',
                content: '需要获取您的位置才能显示附近场地，请允许授权',
                confirmText: '去设置',
                cancelText: '取消',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (res) => {
                        if (res.authSetting['scope.userLocation']) {
                          that.getLocationInfo();
                        }
                      }
                    });
                  } else {
                    // 用户取消，使用默认位置
                    that.useDefaultLocation();
                  }
                }
              });
            }
          });
        } else {
          // 已授权，直接获取位置
          that.getLocationInfo();
        }
      },
      fail: () => {
        // 获取设置失败，使用默认位置
        that.useDefaultLocation();
      }
    });
  },

  // 获取位置信息
  getLocationInfo: function() {
    const that = this;
    wx.getLocation({
      type: 'gcj02',
      isHighAccuracy: true, // 使用高精度定位
      highAccuracyExpireTime: 3000, // 高精度定位超时时间，单位ms
      success: function(res) {
        const latitude = res.latitude;
        const longitude = res.longitude;

        that.setData({
          latitude,
          longitude
        });

        // 更新场馆距离
        that.updateVenueDistance(latitude, longitude);
      },
      fail: function(err) {
        console.error('获取位置失败', err);
        // 获取位置失败，使用默认位置
        that.useDefaultLocation();
      }
    });
  },

  // 使用默认位置
  useDefaultLocation: function() {
    wx.showToast({
      title: '使用默认位置',
      icon: 'none'
    });

    // 使用默认位置（上海市中心）
    const latitude = 31.2304;
    const longitude = 121.4737;

    this.setData({
      latitude,
      longitude
    });

    // 更新场馆距离
    this.updateVenueDistance(latitude, longitude);
  },

  // 加载场馆数据
  loadVenueData: function() {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    // 从API获取数据（现在使用mock数据）
    api.getVenues()
      .then(res => {
        wx.hideLoading();
        // 使用API返回的数据
        const venuesData = res.data || [];

        // 确保所有必要的字段都存在
        const venues = venuesData.map(venue => {
          // 确保sports字段存在
          if (!venue.sports) {
            venue.sports = [];
          }
          // 确保其他必要字段存在
          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: venue.distance || 0,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        // 生成地图标记点
        const markers = venues.map((venue) => {
          return {
            id: venue.id,
            latitude: venue.latitude,
            longitude: venue.longitude,
            // 使用更兼容的标记点样式
            iconPath: '/images/location-pin.svg', // 使用SVG图标
            width: 24,
            height: 32,
            callout: {
              content: venue.name,
              color: '#000000',
              fontSize: 12,
              borderRadius: 5,
              padding: 10,
              display: 'ALWAYS', // 改为始终显示
              textAlign: 'center',
              bgColor: '#FFFFFF',
              borderColor: '#E74C3C'
            }
          };
        });

        // 如果有场馆数据，默认选中第一个
        const selectedVenueId = venues.length > 0 ? venues[0].id : null;

        that.setData({
          venues,
          markers,
          selectedVenueId,
          currentSwiperIndex: 0
        });

        // 更新场馆距离
        if (that.data.latitude && that.data.longitude) {
          that.updateVenueDistance(that.data.latitude, that.data.longitude);
        }

        // 强制重新渲染
        setTimeout(() => {
          that.setData({
            forceUpdate: Date.now()
          });
        }, 100);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取场馆数据失败', err);
        wx.showToast({
          title: '获取场馆数据失败',
          icon: 'none'
        });
      });
  },

  // 更新场馆距离
  updateVenueDistance: function(userLat, userLng) {
    const venues = this.data.venues.map(venue => {
      // 计算距离（实际项目中应使用更精确的算法）
      const distance = this.calculateDistance(
        userLat, userLng,
        venue.latitude, venue.longitude
      );

      return {
        ...venue,
        distance: Math.round(distance)
      };
    });

    this.setData({
      venues
    });
  },

  // 计算两点之间的距离（简化版，仅作演示）
  calculateDistance: function(lat1, lng1, lat2, lng2) {
    const R = 6371000; // 地球半径，单位米
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return distance;
  },

  deg2rad: function(deg) {
    return deg * (Math.PI/180);
  },

  // 搜索框输入事件
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 清除搜索关键词
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    // 重新加载所有场馆数据
    this.loadVenueData();
  },

  // 搜索确认事件
  onSearch: function() {
    const keyword = this.data.searchKeyword;
    if (!keyword) {
      // 如果关键词为空，重新加载所有场馆数据
      this.loadVenueData();
      return;
    }

    const that = this;
    wx.showLoading({
      title: '搜索中...',
    });

    // 调用API搜索场馆（现在使用mock数据）
    api.searchVenues(keyword)
      .then(res => {
        wx.hideLoading();
        // 使用API返回的数据
        const filteredVenues = res.data || [];

        if (filteredVenues.length === 0) {
          wx.showToast({
            title: '未找到相关场馆',
            icon: 'none'
          });
          // 清空场馆数据
          that.setData({
            venues: [],
            markers: [],
            selectedVenueId: null,
            currentSwiperIndex: 0
          });
          return;
        }

        // 确保所有必要的字段都存在
        const processedVenues = filteredVenues.map(venue => {
          // 确保sports字段存在
          if (!venue.sports) {
            venue.sports = [];
          }
          // 确保其他必要字段存在
          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: venue.distance || 0,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        // 更新场馆列表和标记点
        const markers = processedVenues.map(venue => {
          return {
            id: venue.id,
            latitude: venue.latitude,
            longitude: venue.longitude,
            // 使用更兼容的标记点样式
            iconPath: '/images/location-pin.svg', // 使用SVG图标
            width: 24,
            height: 32,
            callout: {
              content: venue.name,
              color: '#000000',
              fontSize: 12,
              borderRadius: 5,
              padding: 10,
              display: 'ALWAYS', // 改为始终显示
              textAlign: 'center',
              bgColor: '#FFFFFF',
              borderColor: '#E74C3C'
            }
          };
        });

        // 如果有场馆数据，默认选中第一个
        const selectedVenueId = processedVenues.length > 0 ? processedVenues[0].id : null;

        // 使用处理后的场馆数据
        that.setData({
          venues: processedVenues,
          markers,
          selectedVenueId,
          currentSwiperIndex: 0
        });

        // 更新场馆距离
        if (that.data.latitude && that.data.longitude) {
          that.updateVenueDistance(that.data.latitude, that.data.longitude);
        }

        // 强制重新渲染
        setTimeout(() => {
          that.setData({
            forceUpdate: Date.now()
          });
        }, 100);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('搜索场馆失败', err);
        wx.showToast({
          title: '搜索场馆失败',
          icon: 'none'
        });
      });
  },

  // 标记点点击事件
  onMarkerTap: function(e) {
    const markerId = e.markerId;
    const venue = this.data.venues.find(v => v.id === markerId);

    if (venue) {
      // 找到场地在数组中的索引
      const index = this.data.venues.findIndex(v => v.id === venue.id);

      // 更新选中的场地ID和swiper索引
      this.setData({
        selectedVenueId: venue.id,
        currentSwiperIndex: index,
        scale: 16 // 放大地图
      });

      // 使用智能移动函数（根据目标点是否在视图内决定平滑移动或瞬移）
      this.smoothMoveToLocation(venue.latitude, venue.longitude);

      // 导航到场馆详情页
      wx.navigateTo({
        url: `/pages/venue-detail/venue-detail?id=${venue.id}`
      });
    }
  },

  // 滚动到指定场地卡片
  scrollToVenueCard: function(venueId) {
    // 找到场地在数组中的索引
    const index = this.data.venues.findIndex(v => v.id === venueId);
    if (index !== -1) {
      // 设置正在切换标志，防止重复触发
      this.setData({
        swiperChanging: true,
        currentSwiperIndex: index
      });

      // 延迟重置切换标志
      setTimeout(() => {
        this.setData({
          swiperChanging: false
        });
      }, 300);
    }
  },

  // 处理轮播图切换事件
  onVenueSwiperChange: function(e) {
    // 如果是程序触发的切换，不处理
    if (this.data.swiperChanging) return;

    const currentIndex = e.detail.current;
    const { venues } = this.data;

    // 确保索引有效
    if (currentIndex >= 0 && currentIndex < venues.length) {
      const venue = venues[currentIndex];

      // 更新选中的场地ID
      this.setData({
        selectedVenueId: venue.id,
        scale: 16 // 放大地图
      });

      // 使用智能移动函数（根据目标点是否在视图内决定平滑移动或瞬移）
      this.smoothMoveToLocation(venue.latitude, venue.longitude);
    }
  },

  // 场馆卡片点击事件
  onVenueCardTap: function(e) {
    const venueId = e.currentTarget.dataset.id;
    const venue = this.data.venues.find(v => v.id === venueId);

    if (venue) {
      // 找到场地在数组中的索引
      const index = this.data.venues.findIndex(v => v.id === venue.id);

      // 更新选中的场地ID和轮播图索引
      this.setData({
        selectedVenueId: venue.id,
        currentSwiperIndex: index, // 更新轮播图索引，使卡片居中
        scale: 16 // 放大地图
      });

      // 使用智能移动函数（根据目标点是否在视图内决定平滑移动或瞬移）
      this.smoothMoveToLocation(venue.latitude, venue.longitude);

      // 导航到场馆详情页
      wx.navigateTo({
        url: `/pages/venue-detail/venue-detail?id=${venue.id}`
      });
    }
  },

  // 切换列表视图
  toggleListView: function() {
    // 跳转到场馆列表页面
    wx.navigateTo({
      url: '/pages/venue-list/venue-list'
    });
  },

  // 导航到我的订单页面
  navigateToMyOrders: function() {
    wx.navigateTo({
      url: '/pages/my-orders/my-orders'
    });
  },

  // 导航到企事业单位列表页面
  navigateToOrganizations: function() {
    wx.navigateTo({
      url: '/pages/organizations/organizations'
    });
  },

  // 导航到运动社团页面
  navigateToClubs: function() {
    wx.navigateTo({
      url: '/pages/clubs/clubs'
    });
  },

  // 导航到我的页面
  navigateToMy: function() {
    wx.navigateTo({
      url: '/pages/my/my'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 移动到当前位置
  moveToLocation: function() {
    const mapCtx = wx.createMapContext('venueMap');

    // 使用地图上下文的moveToLocation方法
    mapCtx.moveToLocation({
      success: () => {
        // 重置地图缩放级别
        this.setData({
          scale: 14
        });
      },
      fail: () => {
        // 如果移动失败，重新获取位置
        this.getUserLocation();

        // 重置地图缩放级别
        this.setData({
          scale: 14
        });
      }
    });
  },

  // 根据目标点是否在视图内决定平滑移动或瞬移
  smoothMoveToLocation: function(latitude, longitude) {
    const mapCtx = wx.createMapContext('venueMap');
    const that = this;

    // 获取当前选中的场馆ID
    const selectedVenueId = this.data.selectedVenueId;

    // 获取当前地图视野范围
    mapCtx.getRegion({
      success: function(res) {
        // 检查目标点是否在当前视图范围内
        const southwest = res.southwest; // 西南角坐标
        const northeast = res.northeast; // 东北角坐标

        const isInView = (
          latitude >= southwest.latitude &&
          latitude <= northeast.latitude &&
          longitude >= southwest.longitude &&
          longitude <= northeast.longitude
        );

        if (isInView) {
          // 如果在视图范围内，平滑移动
          that.smoothMoveWithinView(latitude, longitude, selectedVenueId);
        } else {
          // 如果不在视图范围内，直接瞬移
          that.jumpToLocation(latitude, longitude, selectedVenueId);
        }
      },
      fail: function() {
        // 如果获取视图范围失败，默认使用瞬移
        that.jumpToLocation(latitude, longitude, selectedVenueId);
      }
    });
  },

  // 在视图范围内平滑移动
  smoothMoveWithinView: function(latitude, longitude, venueId) {
    const that = this;

    // 使用动画效果平滑移动地图中心
    const frames = 90; // 动画帧数
    const duration = 500; // 动画持续时间(毫秒)
    const frameInterval = duration / frames;

    // 获取当前位置
    const startLat = this.data.latitude;
    const startLng = this.data.longitude;

    // 计算每一帧的移动量
    const latDiff = (latitude - startLat) / frames;
    const lngDiff = (longitude - startLng) / frames;

    // 执行动画
    let currentFrame = 0;

    const animateFrame = function() {
      currentFrame++;

      // 计算当前帧的位置
      const currentLat = startLat + latDiff * currentFrame;
      const currentLng = startLng + lngDiff * currentFrame;

      // 更新地图位置，不改变缩放级别
      that.setData({
        latitude: currentLat,
        longitude: currentLng
      });

      // 如果动画未完成，继续下一帧
      if (currentFrame < frames) {
        setTimeout(animateFrame, frameInterval);
      } else {
        // 动画完成后，确保显示气泡
        that.showCallout(venueId);
      }
    };

    // 开始动画
    animateFrame();
  },

  // 瞬移到指定位置（无抖动效果）
  jumpToLocation: function(latitude, longitude, venueId) {
    // 直接瞬移到目标位置
    this.setData({
      latitude: latitude,
      longitude: longitude
    });

    // 瞬移完成后，确保显示气泡
    this.showCallout(venueId);
  },

  // 显示指定标记点的气泡
  showCallout: function(markerId) {
    if (!markerId) return;

    const mapCtx = wx.createMapContext('venueMap');

    // 使用地图上下文的showCallout方法显示气泡
    setTimeout(() => {
      mapCtx.showCallout({
        markerId: markerId,
        success: () => {
          console.log('气泡显示成功');
        },
        fail: (error) => {
          console.error('气泡显示失败', error);
        }
      });
    }, 100); // 短暂延迟，确保地图已经更新
  }
})
