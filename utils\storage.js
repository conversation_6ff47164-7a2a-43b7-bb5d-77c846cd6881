// utils/storage.js
// 本地存储工具类

/**
 * 保存订单数据到本地存储
 * @param {Object} order 订单数据
 * @returns {String} 订单ID
 */
const saveOrder = (order) => {
  try {
    // 获取现有订单列表
    const orders = getOrders();

    // 确保订单有ID
    if (!order.id) {
      order.id = 'ORDER_' + Date.now();
    }

    // 添加订单到列表
    orders.push(order);

    // 保存更新后的订单列表
    wx.setStorageSync('orders', orders);

    return order.id;
  } catch (e) {
    console.error('保存订单失败', e);
    return null;
  }
};

/**
 * 获取所有订单
 * @returns {Array} 订单列表
 */
const getOrders = () => {
  try {
    const orders = wx.getStorageSync('orders');
    return orders ? orders : [];
  } catch (e) {
    console.error('获取订单列表失败', e);
    return [];
  }
};

/**
 * 根据ID获取订单
 * @param {String} orderId 订单ID
 * @returns {Object|null} 订单数据或null
 */
const getOrderById = (orderId) => {
  try {
    const orders = getOrders();
    return orders.find(order => order.id === orderId) || null;
  } catch (e) {
    console.error('获取订单详情失败', e);
    return null;
  }
};

/**
 * 更新订单状态
 * @param {String} orderId 订单ID
 * @param {String} status 新状态
 * @param {String} statusText 状态文本描述
 * @returns {Boolean} 是否更新成功
 */
const updateOrderStatus = (orderId, status, statusText) => {
  try {
    const orders = getOrders();
    const index = orders.findIndex(order => order.id === orderId);

    if (index === -1) return false;

    orders[index].status = status;
    orders[index].statusText = statusText;

    wx.setStorageSync('orders', orders);
    return true;
  } catch (e) {
    console.error('更新订单状态失败', e);
    return false;
  }
};

/**
 * 更新订单数据
 * @param {String} orderId 订单ID
 * @param {Object} updatedOrder 更新后的订单数据
 * @returns {Boolean} 是否更新成功
 */
const updateOrderById = (orderId, updatedOrder) => {
  try {
    const orders = getOrders();
    const index = orders.findIndex(order => order.id === orderId);

    if (index === -1) return false;

    orders[index] = updatedOrder;

    wx.setStorageSync('orders', orders);
    return true;
  } catch (e) {
    console.error('更新订单数据失败', e);
    return false;
  }
};

/**
 * 删除订单
 * @param {String} orderId 订单ID
 * @returns {Boolean} 是否删除成功
 */
const deleteOrder = (orderId) => {
  try {
    let orders = getOrders();
    orders = orders.filter(order => order.id !== orderId);

    wx.setStorageSync('orders', orders);
    return true;
  } catch (e) {
    console.error('删除订单失败', e);
    return false;
  }
};

/**
 * 清空所有订单
 * @returns {Boolean} 是否清空成功
 */
const clearOrders = () => {
  try {
    wx.removeStorageSync('orders');
    return true;
  } catch (e) {
    console.error('清空订单失败', e);
    return false;
  }
};

/**
 * 保存认证记录
 * @param {Object} certification 认证记录
 * @returns {Boolean} 是否保存成功
 */
const saveCertification = (certification) => {
  try {
    // 获取现有认证记录
    const certifications = getCertifications();

    // 确保认证记录有ID
    if (!certification.id) {
      certification.id = 'CERT_' + Date.now();
    }

    // 检查是否已存在该单位的认证
    const existingIndex = certifications.findIndex(cert => cert.orgId === certification.orgId);

    if (existingIndex !== -1) {
      // 更新现有认证
      certifications[existingIndex] = certification;
    } else {
      // 添加新认证
      certifications.push(certification);
    }

    // 保存更新后的认证记录
    wx.setStorageSync('certifications', certifications);

    return true;
  } catch (e) {
    console.error('保存认证记录失败', e);
    return false;
  }
};

/**
 * 获取所有认证记录
 * @returns {Array} 认证记录列表
 */
const getCertifications = () => {
  try {
    const certifications = wx.getStorageSync('certifications');
    return certifications ? certifications : [];
  } catch (e) {
    console.error('获取认证记录列表失败', e);
    return [];
  }
};

/**
 * 根据单位ID获取认证记录
 * @param {String} orgId 单位ID
 * @returns {Object|null} 认证记录或null
 */
const getCertificationByOrgId = (orgId) => {
  try {
    const certifications = getCertifications();
    return certifications.find(cert => cert.orgId === orgId) || null;
  } catch (e) {
    console.error('获取认证记录失败', e);
    return null;
  }
};

/**
 * 取消认证
 * @param {String} orgId 单位ID
 * @returns {Boolean} 是否取消成功
 */
const cancelCertification = (orgId) => {
  try {
    let certifications = getCertifications();
    certifications = certifications.filter(cert => cert.orgId !== orgId);

    wx.setStorageSync('certifications', certifications);
    return true;
  } catch (e) {
    console.error('取消认证失败', e);
    return false;
  }
};

/**
 * 创建运动社团
 * @param {Object} club 社团数据
 * @returns {String} 社团ID
 */
const createClub = (club) => {
  try {
    // 获取现有社团列表
    const clubs = getClubs();

    // 确保社团有ID
    if (!club.id) {
      club.id = 'CLUB_' + Date.now();
    }

    // 确保社团有创建时间
    if (!club.createdAt) {
      club.createdAt = new Date().toISOString();
    }

    // 添加社团到列表
    clubs.push(club);

    // 保存更新后的社团列表
    wx.setStorageSync('clubs', clubs);

    return club.id;
  } catch (e) {
    console.error('创建社团失败', e);
    return null;
  }
};

/**
 * 获取所有社团
 * @returns {Array} 社团列表
 */
const getClubs = () => {
  try {
    console.log('getClubs called');
    const clubs = wx.getStorageSync('clubs');
    console.log('Clubs from storage:', clubs);
    return clubs ? clubs : [];
  } catch (e) {
    console.error('获取社团列表失败', e);
    return [];
  }
};

/**
 * 根据ID获取社团
 * @param {String} clubId 社团ID
 * @returns {Object|null} 社团数据或null
 */
const getClubById = (clubId) => {
  try {
    console.log('getClubById called with clubId:', clubId);
    const clubs = getClubs();
    const club = clubs.find(club => club.id === clubId) || null;
    console.log('Club found by ID:', club);
    return club;
  } catch (e) {
    console.error('获取社团详情失败', e);
    return null;
  }
};

/**
 * 更新社团信息
 * @param {String} clubId 社团ID
 * @param {Object} updatedClub 更新后的社团数据
 * @returns {Boolean} 是否更新成功
 */
const updateClub = (clubId, updatedClub) => {
  try {
    console.log('updateClub called with clubId:', clubId, 'updatedClub:', updatedClub);

    const clubs = getClubs();
    console.log('Current clubs:', clubs);

    const index = clubs.findIndex(club => club.id === clubId);
    console.log('Club index:', index);

    if (index === -1) {
      console.log('Club not found in clubs array');
      return false;
    }

    clubs[index] = { ...clubs[index], ...updatedClub };
    console.log('Updated club:', clubs[index]);

    wx.setStorageSync('clubs', clubs);
    console.log('Clubs saved to storage');

    return true;
  } catch (e) {
    console.error('更新社团信息失败', e);
    return false;
  }
};

/**
 * 删除社团
 * @param {String} clubId 社团ID
 * @returns {Boolean} 是否删除成功
 */
const deleteClub = (clubId) => {
  try {
    let clubs = getClubs();
    clubs = clubs.filter(club => club.id !== clubId);

    wx.setStorageSync('clubs', clubs);
    return true;
  } catch (e) {
    console.error('删除社团失败', e);
    return false;
  }
};

/**
 * 加入社团
 * @param {String} clubId 社团ID
 * @param {Object} member 成员信息
 * @returns {Boolean} 是否加入成功
 */
const joinClub = (clubId, member) => {
  try {
    console.log('joinClub in storage.js called with clubId:', clubId, 'member:', member);

    const club = getClubById(clubId);
    console.log('club found in storage:', club);

    if (!club) {
      console.log('Club not found in storage');
      return false;
    }

    // 确保社团有成员列表
    if (!club.members) {
      console.log('Initializing members array');
      club.members = [];
    }

    // 检查是否已经是成员
    const isMember = club.members.some(m => m.id === member.id);
    console.log('Is already a member:', isMember);

    if (isMember) return true;

    // 添加成员
    const newMember = {
      ...member,
      joinedAt: new Date().toISOString(),
      status: 'pending' // 申请状态：pending, approved, rejected
    };
    console.log('Adding new member:', newMember);

    club.members.push(newMember);

    // 更新社团
    const updateResult = updateClub(clubId, club);
    console.log('Update result:', updateResult);

    return updateResult;
  } catch (e) {
    console.error('加入社团失败', e);
    return false;
  }
};

/**
 * 审批社团成员
 * @param {String} clubId 社团ID
 * @param {String} memberId 成员ID
 * @param {String} status 审批状态：approved, rejected
 * @returns {Boolean} 是否审批成功
 */
const approveClubMember = (clubId, memberId, status) => {
  try {
    const club = getClubById(clubId);
    if (!club || !club.members) return false;

    // 查找成员
    const memberIndex = club.members.findIndex(m => m.id === memberId);
    if (memberIndex === -1) return false;

    // 更新成员状态
    club.members[memberIndex].status = status;
    club.members[memberIndex].approvedAt = new Date().toISOString();

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('审批社团成员失败', e);
    return false;
  }
};

/**
 * 更新社团成员角色
 * @param {String} clubId 社团ID
 * @param {String} memberId 成员ID
 * @param {String} role 成员角色：admin, member
 * @returns {Boolean} 是否更新成功
 */
const updateClubMemberRole = (clubId, memberId, role) => {
  try {
    const club = getClubById(clubId);
    if (!club || !club.members) return false;

    // 查找成员
    const memberIndex = club.members.findIndex(m => m.id === memberId);
    if (memberIndex === -1) return false;

    // 更新成员角色
    club.members[memberIndex].role = role;

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('更新社团成员角色失败', e);
    return false;
  }
};

/**
 * 退出社团
 * @param {String} clubId 社团ID
 * @param {String} memberId 成员ID
 * @returns {Boolean} 是否退出成功
 */
const leaveClub = (clubId, memberId) => {
  try {
    const club = getClubById(clubId);
    if (!club || !club.members) return false;

    // 移除成员
    club.members = club.members.filter(m => m.id !== memberId);

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('退出社团失败', e);
    return false;
  }
};

/**
 * 发布社团公告
 * @param {String} clubId 社团ID
 * @param {Object} announcement 公告信息
 * @returns {Boolean} 是否发布成功
 */
const addClubAnnouncement = (clubId, announcement) => {
  try {
    const club = getClubById(clubId);
    if (!club) return false;

    // 确保社团有公告列表
    if (!club.announcements) {
      club.announcements = [];
    }

    // 添加公告
    club.announcements.push({
      id: 'ANN_' + Date.now(),
      createdAt: new Date().toISOString(),
      ...announcement
    });

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('发布社团公告失败', e);
    return false;
  }
};

/**
 * 发布社团动态
 * @param {String} clubId 社团ID
 * @param {Object} activity 动态信息
 * @returns {String|null} 动态ID或null
 */
const addClubActivity = (clubId, activity) => {
  try {
    const club = getClubById(clubId);
    if (!club) return null;

    // 确保社团有动态列表
    if (!club.activities) {
      club.activities = [];
    }

    // 生成动态ID
    const activityId = 'ACT_' + Date.now();

    // 添加动态
    club.activities.push({
      id: activityId,
      createdAt: new Date().toISOString(),
      likes: [],
      comments: [],
      ...activity
    });

    // 更新社团
    if (updateClub(clubId, club)) {
      return activityId;
    }
    return null;
  } catch (e) {
    console.error('发布社团动态失败', e);
    return null;
  }
};

/**
 * 点赞社团动态
 * @param {String} clubId 社团ID
 * @param {String} activityId 动态ID
 * @param {String} userId 用户ID
 * @returns {Boolean} 是否点赞成功
 */
const likeClubActivity = (clubId, activityId, userId) => {
  try {
    const club = getClubById(clubId);
    if (!club || !club.activities) return false;

    // 查找动态
    const activityIndex = club.activities.findIndex(a => a.id === activityId);
    if (activityIndex === -1) return false;

    // 确保动态有点赞列表
    if (!club.activities[activityIndex].likes) {
      club.activities[activityIndex].likes = [];
    }

    // 检查是否已点赞
    const hasLiked = club.activities[activityIndex].likes.includes(userId);

    if (hasLiked) {
      // 取消点赞
      club.activities[activityIndex].likes = club.activities[activityIndex].likes.filter(id => id !== userId);
    } else {
      // 添加点赞
      club.activities[activityIndex].likes.push(userId);
    }

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('点赞社团动态失败', e);
    return false;
  }
};

/**
 * 评论社团动态
 * @param {String} clubId 社团ID
 * @param {String} activityId 动态ID
 * @param {Object} comment 评论信息
 * @returns {Boolean} 是否评论成功
 */
const commentClubActivity = (clubId, activityId, comment) => {
  try {
    const club = getClubById(clubId);
    if (!club || !club.activities) return false;

    // 查找动态
    const activityIndex = club.activities.findIndex(a => a.id === activityId);
    if (activityIndex === -1) return false;

    // 确保动态有评论列表
    if (!club.activities[activityIndex].comments) {
      club.activities[activityIndex].comments = [];
    }

    // 添加评论
    club.activities[activityIndex].comments.push({
      id: 'COMMENT_' + Date.now(),
      createdAt: new Date().toISOString(),
      ...comment
    });

    // 更新社团
    return updateClub(clubId, club);
  } catch (e) {
    console.error('评论社团动态失败', e);
    return false;
  }
};

export {
  saveOrder,
  getOrders,
  getOrderById,
  updateOrderStatus,
  updateOrderById,
  deleteOrder,
  clearOrders,
  saveCertification,
  getCertifications,
  getCertificationByOrgId,
  cancelCertification,
  createClub,
  getClubs,
  getClubById,
  updateClub,
  deleteClub,
  joinClub,
  approveClubMember,
  updateClubMemberRole,
  leaveClub,
  addClubAnnouncement,
  addClubActivity,
  likeClubActivity,
  commentClubActivity
};
