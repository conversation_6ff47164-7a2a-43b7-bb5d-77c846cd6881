/* pages/map-poster/map-poster.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 为底部导航栏预留空间 */
  box-sizing: border-box;
  padding-top: calc(100rpx + env(safe-area-inset-top)); /* 为导航栏预留空间，包括安全区域 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 导航栏样式 */
.nav-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100rpx; /* 增加高度 */
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto; /* 确保整体居中 */
  text-align: center; /* 文本居中 */
}

.nav-logo {
  height: 60rpx;
  width: auto;
  margin-right: 10rpx; /* 稍微增加间距 */
  vertical-align: middle; /* 确保垂直对齐 */
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

/* 地图容器 */
.map-container {
  margin-top: 20rpx; /* 添加顶部边距 */
  margin-bottom: 20rpx; /* 添加底部边距 */
  width: 90%; /* 减小宽度为90%，与JS中的画布宽度一致 */
  display: flex;
  justify-content: center;
  align-items: center; /* 垂直居中 */
  padding: 40rpx 0 20rpx 0; /* 增加顶部内边距，为标题留出空间 */
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx; /* 保留轻微圆角 */
  height: auto; /* 自适应高度 */
  margin-left: auto; /* 使用auto使容器居中 */
  margin-right: auto; /* 使用auto使容器居中 */
  background: linear-gradient(to bottom, #f9f9f9, #eeeeee); /* 使用与JS中相同的中性灰色渐变背景 */
}

/* 功能入口 */
.function-entries {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  padding: 20rpx 30rpx;
  margin: 0; /* 移除上下边距 */
  background-color: transparent; /* 移除背景色 */
  box-sizing: border-box;
  flex-wrap: wrap; /* 允许按钮换行 */
}

.function-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  margin: 0 10rpx;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #E74C3C;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  position: relative;
}

.function-label {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 认证图标 */
.auth-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 订单图标 */
.order-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 社团图标 */
.club-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 地图图标 */
.map-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 我的图标 */
.my-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 场馆列表 */
.venue-list {
  width: 100%;
  padding: 10rpx 20rpx; /* 减小上下内边距 */
  background-color: #f5f5f5;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.venues-scroll-view {
  height: 550rpx; /* 进一步减小高度，为底部导航栏留出空间 */
  background-color: #f5f5f5;
  width: 100%;
  margin-bottom: 20rpx; /* 添加底部边距 */
}

.venue-list-content {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
}

.venue-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: transparent;
}

.venue-list-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

/* 添加红色竖线装饰 */
.venue-list-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background-color: #E74C3C;
  border-radius: 4rpx;
}

.venue-list-filter {
  display: flex;
  align-items: center;
}

.filter-badge {
  background-color: #E74C3C;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  margin-right: 8px;
}

.show-all-btn {
  color: #E74C3C;
  font-size: 14px;
  padding: 4px 8px;
  border: 1px solid #E74C3C;
  border-radius: 12px;
}

.venue-card {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx; /* 增加卡片圆角 */
  overflow: hidden;
  align-items: center; /* 添加垂直居中 */
  padding-right: 10rpx; /* 右侧添加内边距，平衡左侧图片的边距 */
}

.venue-card-selected {
  background-color: #fff; /* 保持白色背景 */
}

.venue-image {
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
  display: block; /* 确保图片正确显示 */
  object-fit: cover; /* 保持图片比例并填充容器 */
  border-radius: 10rpx; /* 添加圆角 */
  margin: 10rpx; /* 添加边距，使圆角效果更明显 */
}

.venue-info {
  flex: 1;
  padding: 20rpx;
  position: relative;
}

.venue-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.venue-type {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.venue-address {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

/* 运动类型标签样式 */
.venue-sports {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.sport-tag {
  font-size: 22rpx;
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 0 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-tag {
  background-color: rgba(231, 76, 60, 0.1);
}

/* 移除了装饰性角落元素的样式 */

/* 地图标题 */
.map-title {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx; /* 缩小字体 */
  color: #333;
  z-index: 5;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9); /* 整体缩小到90% */
  transform-origin: bottom right; /* 从右下角开始缩放 */
}

.map-title-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1.3; /* 减小行高 */
}

.map-title-icon {
  width: 24rpx; /* 缩小图标 */
  height: 24rpx; /* 缩小图标 */
  margin-right: 8rpx; /* 减小间距 */
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23E74C3C"><path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/></svg>');
  background-size: 24rpx 24rpx; /* 缩小图标尺寸 */
  background-position: center;
  background-repeat: no-repeat;
}

.map-canvas {
  display: block;
  margin: 0 auto; /* 居中显示 */
  border-radius: 6rpx;
  overflow: hidden;
  max-width: 100%; /* 确保不超出容器 */
}

/* 地图加载覆盖层 */
.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-icon {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #E74C3C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载提示 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid #eee;
  padding-bottom: env(safe-area-inset-bottom); /* 适配iPhone底部安全区域 */
  z-index: 100;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.tab-item.active {
  color: #E74C3C;
}

.tab-icon {
  width: 50rpx;
  height: 50rpx;
  position: relative;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #666;
}

.tab-item.active .tab-text {
  color: #E74C3C;
}

/* 首页图标 */
.poster-icon::after {
  content: "";
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23E74C3C"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 调整地图图标和我的图标在底部导航栏中的样式 */
.tab-item .map-icon::after,
.tab-item .my-icon::after {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-size: 40rpx 40rpx;
}

.tab-item:not(.active) .map-icon::after {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666666"><path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/></svg>');
}

.tab-item:not(.active) .my-icon::after {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666666"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
}
