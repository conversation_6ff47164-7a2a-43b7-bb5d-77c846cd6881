// pages/my-clubs/my-clubs.js
import { getClubs } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    userId: 'user_123', // 使用固定ID以便与模拟数据中的创建者ID匹配
    clubs: [],
    joinedClubs: [],
    pendingClubs: [],
    createdClubs: [],
    currentTab: 'joined' // 当前选中的标签: joined, pending, created
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
    this.loadClubs();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 每次页面显示时重新加载数据，确保数据最新
    this.loadClubs();
  },

  // 加载社团数据
  loadClubs: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 获取所有社团
    const allClubs = getClubs();
    
    // 筛选出用户创建的社团
    const createdClubs = allClubs.filter(club => 
      club.creatorId === this.data.userId
    );
    
    // 筛选出用户已加入的社团（不包括自己创建的）
    const joinedClubs = allClubs.filter(club => 
      club.creatorId !== this.data.userId && 
      club.members && 
      club.members.some(m => 
        m.id === this.data.userId && 
        m.status === 'approved'
      )
    );
    
    // 筛选出用户申请加入但待审核的社团
    const pendingClubs = allClubs.filter(club => 
      club.members && 
      club.members.some(m => 
        m.id === this.data.userId && 
        m.status === 'pending'
      )
    );

    // 处理社团数据，添加额外信息
    const processClubs = (clubs) => {
      return clubs.map(club => {
        // 计算成员数量
        const approvedMembers = club.members ? club.members.filter(m => m.status === 'approved') : [];
        const memberCount = approvedMembers.length;
        
        return {
          ...club,
          memberCount
        };
      });
    };

    this.setData({
      clubs: allClubs,
      joinedClubs: processClubs(joinedClubs),
      pendingClubs: processClubs(pendingClubs),
      createdClubs: processClubs(createdClubs),
      loading: false
    });

    wx.hideLoading();
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.currentTab) return;
    
    this.setData({
      currentTab: tab
    });
  },

  // 导航到社团详情
  navigateToClubDetail: function(e) {
    const clubId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/club-detail/club-detail?id=${clubId}`
    });
  },

  // 取消加入申请
  cancelJoinRequest: function(e) {
    const clubId = e.currentTarget.dataset.id;
    const club = this.data.clubs.find(c => c.id === clubId);
    
    if (!club) return;
    
    wx.showModal({
      title: '取消申请',
      content: `确定要取消加入"${club.name}"的申请吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用取消申请的API
          const { leaveClub } = require('../../utils/storage');
          const success = leaveClub(clubId, this.data.userId);
          
          if (success) {
            wx.showToast({
              title: '已取消申请',
              icon: 'success'
            });
            
            // 重新加载数据
            this.loadClubs();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 创建新社团
  navigateToCreateClub: function() {
    wx.navigateTo({
      url: '/pages/create-club/create-club'
    });
  },

  // 浏览所有社团
  navigateToAllClubs: function() {
    wx.navigateTo({
      url: '/pages/clubs/clubs'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
})
