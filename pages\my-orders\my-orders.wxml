<!--pages/my-orders/my-orders.wxml-->
<view class="my-orders-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">订单中心</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 订单类型选项卡 -->
    <view class="order-tabs">
      <view class="tab-header">
        <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
        <view class="tab-item {{currentTab === 'pending' ? 'active' : ''}}" bindtap="switchTab" data-tab="pending">待支付</view>
        <view class="tab-item {{currentTab === 'processing' ? 'active' : ''}}" bindtap="switchTab" data-tab="processing">进行中</view>
        <view class="tab-item {{currentTab === 'refunded' ? 'active' : ''}}" bindtap="switchTab" data-tab="refunded">已退款</view>
        <view class="tab-item {{currentTab === 'completed' ? 'active' : ''}}" bindtap="switchTab" data-tab="completed">已完成</view>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 订单列表 -->
    <scroll-view class="order-list" scroll-y="true" wx:if="{{!loading}}">
      <!-- 无订单提示 -->
      <view class="no-orders" wx:if="{{filteredOrders.length === 0}}">
        <view class="no-orders-icon">📋</view>
        <view class="no-orders-text">暂无订单记录</view>
      </view>

      <!-- 订单列表项 -->
      <view class="order-item" wx:for="{{filteredOrders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <!-- 订单编号和状态 -->
        <view class="order-header">
          <view class="order-number">订单号：{{item.orderInfo.orderNumber}}</view>
          <view class="order-status {{item.status}}">{{item.statusText}}</view>
        </view>

        <!-- 场馆信息 -->
        <view class="venue-info">
          <image class="venue-image" src="{{item.venue.imageUrl}}" mode="aspectFill"></image>
          <view class="venue-details">
            <view class="venue-name">{{item.venue.fullName || item.venue.name}}</view>
            <view class="venue-hall">{{item.venue.fullHall || item.venue.hall}}</view>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="order-footer">
          <view class="order-price">实付款：<text class="price">¥{{item.price.final}}</text></view>

          <!-- 操作按钮 -->
          <view class="order-actions">
            <view class="action-button more" catchtap="viewMore">更多</view>
            <view class="action-button scan" catchtap="scanToEnter" data-id="{{item.id}}">扫码签到</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
