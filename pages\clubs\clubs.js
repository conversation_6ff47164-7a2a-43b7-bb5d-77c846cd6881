// pages/clubs/clubs.js
import { getClubs, getClubById, joinClub, updateClub, getCertifications } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    clubs: [],
    filteredClubs: [],
    searchKeyword: '',
    organizations: [], // 保留用于创建社团时选择
    userId: 'user_123', // 使用固定ID以便与模拟数据中的创建者ID匹配

    // 模拟企事业单位数据
    mockOrganizations: [
      {
        id: '1',
        name: '浙江大学',
        type: 'institution',
        address: '浙江省杭州市西湖区余杭塘路866号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江大学是中国著名的综合性研究型大学，位于浙江省杭州市。'
      },
      {
        id: '2',
        name: '阿里巴巴',
        type: 'enterprise',
        address: '浙江省杭州市余杭区文一西路969号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '阿里巴巴集团是全球领先的电子商务公司，提供各种互联网服务。'
      },
      {
        id: '3',
        name: '杭州市第一人民医院',
        type: 'institution',
        address: '浙江省杭州市上城区浣纱路261号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '杭州市第一人民医院是杭州市规模最大的综合性医院之一，提供全面的医疗服务。'
      },
      {
        id: '4',
        name: '海康威视',
        type: 'enterprise',
        address: '浙江省杭州市滨江区阡陌路555号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '海康威视是全球领先的安防产品和解决方案提供商，专注于视频监控技术。'
      },
      {
        id: '5',
        name: '浙江省人民医院',
        type: 'institution',
        address: '浙江省杭州市上城区庆春路158号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江省人民医院是浙江省规模最大的综合性医院之一，提供全面的医疗服务。'
      }
    ],

    // 模拟社团数据（如果本地存储中没有数据，将使用这些）
    mockClubs: [
      {
        id: 'CLUB_1',
        name: '浙大羽毛球社',
        orgId: '1',
        orgName: '浙江大学',
        creatorId: 'user_123',
        sportTypes: ['羽毛球'],
        description: '浙江大学羽毛球爱好者社团，欢迎各位羽毛球爱好者加入！',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        members: [
          {
            id: 'user_123',
            name: '张三',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-01T10:00:00.000Z',
            status: 'approved',
            role: 'creator'
          },
          {
            id: 'user_234',
            name: '李明',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-02T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_345',
            name: '王芳',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-03T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_456',
            name: '赵静',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-04T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_567',
            name: '刘强',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-05T10:00:00.000Z',
            status: 'pending'
          },
          {
            id: 'user_678',
            name: '陈伟',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-06T10:00:00.000Z',
            status: 'pending'
          }
        ],
        announcements: [],
        activities: [],
        createdAt: '2025-05-01T10:00:00.000Z'
      },
      {
        id: 'CLUB_2',
        name: '阿里足球俱乐部',
        orgId: '2',
        orgName: '阿里巴巴',
        creatorId: 'user_456',
        sportTypes: ['足球'],
        description: '阿里巴巴员工足球俱乐部，定期组织足球比赛和训练。',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        members: [
          {
            id: 'user_456',
            name: '李四',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-02T10:00:00.000Z',
            status: 'approved',
            role: 'creator'
          },
          {
            id: 'user_789',
            name: '周杰',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-03T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_890',
            name: '吴敏',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-04T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_901',
            name: '郑华',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-05T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          }
        ],
        announcements: [],
        activities: [],
        createdAt: '2025-05-02T10:00:00.000Z'
      },
      {
        id: 'CLUB_3',
        name: '医院乒乓球协会',
        orgId: '3',
        orgName: '杭州市第一人民医院',
        creatorId: 'user_789',
        sportTypes: ['乒乓球'],
        description: '医院职工乒乓球协会，欢迎医院职工加入。',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        members: [
          {
            id: 'user_789',
            name: '王五',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-03T10:00:00.000Z',
            status: 'approved',
            role: 'creator'
          },
          {
            id: 'user_012',
            name: '孙丽',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-04T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_123',
            name: '我',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-05T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_234',
            name: '李明',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-06T10:00:00.000Z',
            status: 'pending'
          }
        ],
        announcements: [],
        activities: [],
        createdAt: '2025-05-03T10:00:00.000Z'
      },
      {
        id: 'CLUB_4',
        name: '阿里篮球俱乐部',
        orgId: '2',
        orgName: '阿里巴巴',
        creatorId: 'user_456',
        sportTypes: ['篮球'],
        description: '阿里巴巴员工篮球俱乐部，定期组织篮球比赛和训练。',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        members: [
          {
            id: 'user_456',
            name: '李四',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-02T10:00:00.000Z',
            status: 'approved',
            role: 'creator'
          },
          {
            id: 'user_123',
            name: '我',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-04T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_345',
            name: '王芳',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-05T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_567',
            name: '刘强',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-06T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_678',
            name: '陈伟',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-07T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_789',
            name: '王五',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-08T10:00:00.000Z',
            status: 'pending'
          }
        ],
        announcements: [],
        activities: [],
        createdAt: '2025-05-02T10:00:00.000Z'
      },
      {
        id: 'CLUB_5',
        name: '阿里羽毛球俱乐部',
        orgId: '2',
        orgName: '阿里巴巴',
        creatorId: 'user_456',
        sportTypes: ['羽毛球'],
        description: '阿里巴巴员工羽毛球俱乐部，定期组织羽毛球比赛和训练。',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        members: [
          {
            id: 'user_456',
            name: '李四',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-02T10:00:00.000Z',
            status: 'approved',
            role: 'creator'
          },
          {
            id: 'user_890',
            name: '吴敏',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-03T10:00:00.000Z',
            status: 'approved',
            role: 'admin'
          },
          {
            id: 'user_901',
            name: '郑华',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-04T10:00:00.000Z',
            status: 'approved',
            role: 'member'
          },
          {
            id: 'user_123',
            name: '我',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-05T10:00:00.000Z',
            status: 'pending'
          },
          {
            id: 'user_012',
            name: '孙丽',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
            joinedAt: '2025-05-06T10:00:00.000Z',
            status: 'pending'
          }
        ],
        announcements: [],
        activities: [],
        createdAt: '2025-05-02T10:00:00.000Z'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 加载企事业单位数据
    this.loadOrganizations();

    // 保存过滤参数
    if (options && options.filter === 'my') {
      this.setData({
        filterMy: true
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次页面显示时重新加载社团数据
    this.loadClubs();
  },

  // 加载企事业单位数据
  loadOrganizations() {
    // 使用模拟数据
    const organizations = this.data.mockOrganizations;

    this.setData({
      organizations
    });
  },

  // 加载社团数据
  loadClubs() {
    wx.showLoading({
      title: '加载中...',
    });

    // 获取现有社团数据
    let clubs = getClubs();

    // 如果本地存储中没有社团数据，则使用模拟数据初始化
    if (!clubs || clubs.length === 0) {
      console.log('没有找到社团数据，使用模拟数据初始化');

      // 使用模拟数据
      const mockClubs = this.data.mockClubs;

      // 将模拟数据保存到本地存储
      mockClubs.forEach(club => {
        const { createClub } = require('../../utils/storage');
        createClub(club);
      });

      // 重新获取数据
      clubs = getClubs();
    } else {
      console.log('找到现有社团数据:', clubs.length, '个社团');
    }

    // 获取用户认证信息
    const certifications = getCertifications();
    console.log('User certifications:', certifications);

    // 处理社团数据，添加用户相关信息
    const processedClubs = clubs.map(club => {
      // 计算成员数量
      const approvedMembers = club.members ? club.members.filter(m => m.status === 'approved') : [];
      const memberCount = approvedMembers.length;

      // 检查当前用户是否是创建者
      const isCreator = club.creatorId === this.data.userId;

      // 检查当前用户是否是成员
      const isMember = club.members ? club.members.some(m => m.id === this.data.userId && m.status === 'approved') : false;

      // 检查当前用户是否已申请加入但未审批
      const isPending = club.members ? club.members.some(m => m.id === this.data.userId && m.status === 'pending') : false;

      // 查找组织名称
      const org = this.data.organizations.find(o => o.id === club.orgId);
      const orgName = org ? org.name : club.orgName || '未知单位';

      // 检查用户是否已认证该单位（注意：处理字符串和数字类型的兼容性）
      const isCertified = certifications.some(cert =>
        cert.orgId.toString() === club.orgId.toString() && cert.status === 'approved'
      );

      return {
        ...club,
        memberCount,
        isCreator,
        isMember,
        isPending,
        orgName,
        isCertified
      };
    });

    this.setData({
      clubs: processedClubs,
      filteredClubs: processedClubs,
      loading: false
    });

    wx.hideLoading();
  },

  // 搜索社团
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });

    // 输入时自动搜索
    this.filterClubs();
  },

  // 清除搜索关键词
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.filterClubs();
  },

  // 执行搜索
  doSearch() {
    this.filterClubs();
  },

  // 这些方法已不再需要，但保留空方法以防其他地方调用
  toggleFilterDropdown() {
    // 不再需要
  },

  selectOrgFilter() {
    // 不再需要
  },

  // 筛选社团
  filterClubs() {
    const { clubs, searchKeyword, filterMy } = this.data;
    let filtered = clubs;

    // 如果是"我的社团"过滤，只显示用户创建或加入的社团
    if (filterMy) {
      filtered = filtered.filter(club =>
        club.isCreator || club.isMember || club.isPending
      );

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: '我的社团'
      });
    }

    // 应用搜索关键词筛选
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(club =>
        // 搜索社团名称
        club.name.toLowerCase().includes(keyword) ||
        // 搜索社团描述
        club.description.toLowerCase().includes(keyword) ||
        // 搜索运动类型
        club.sportTypes.some(sport => sport.toLowerCase().includes(keyword)) ||
        // 搜索单位名称
        club.orgName.toLowerCase().includes(keyword)
      );
    }

    this.setData({
      filteredClubs: filtered
    });
  },

  // 导航到社团详情页
  navigateToClubDetail(e) {
    const clubId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/club-detail/club-detail?id=${clubId}`
    });
  },

  // 导航到创建社团页面
  navigateToCreateClub() {
    wx.navigateTo({
      url: '/pages/create-club/create-club'
    });
  },

  // 申请加入社团
  joinClub(e) {
    // 在微信小程序中，catchtap 已经阻止了事件冒泡，不需要手动调用 stopPropagation
    console.log('joinClub called', e);

    const clubId = e.currentTarget.dataset.id;
    console.log('clubId:', clubId);

    // 打印所有社团ID，以便调试
    console.log('All club IDs:', this.data.clubs.map(c => c.id));

    const club = this.data.clubs.find(c => c.id === clubId);
    console.log('club found:', club);

    if (!club) {
      console.log('Club not found in this.data.clubs');

      // 尝试直接从存储中获取
      const clubFromStorage = getClubById(clubId);

      if (clubFromStorage) {
        console.log('Club found in storage:', clubFromStorage);
        // 使用从存储中获取的社团继续
        this.checkCertificationAndJoin(clubId, clubFromStorage);
        return;
      }

      console.log('Club not found in storage either');
      return;
    }

    this.checkCertificationAndJoin(clubId, club);
  },

  // 检查用户是否已认证该单位，然后加入社团
  checkCertificationAndJoin(clubId, club) {
    // 获取用户的认证记录
    const certifications = getCertifications();
    console.log('User certifications:', certifications);
    console.log('Club orgId:', club.orgId);

    // 检查用户是否已认证该单位（注意：处理字符串和数字类型的兼容性）
    const hasCertification = certifications.some(cert =>
      cert.orgId.toString() === club.orgId.toString() && cert.status === 'approved'
    );

    if (!hasCertification) {
      // 用户未认证该单位，显示提示
      wx.showModal({
        title: '无法申请',
        content: `您尚未认证"${club.orgName}"，只能申请加入已认证单位的社团`,
        confirmText: '去认证',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到单位列表页面
            wx.navigateTo({
              url: '/pages/organizations/organizations'
            });
          }
        }
      });
      return;
    }

    // 用户已认证该单位，显示加入确认对话框
    this.showJoinConfirmation(clubId, club);
  },

  // 显示加入确认对话框
  showJoinConfirmation(clubId, club) {
    wx.showModal({
      title: '申请加入',
      content: `确定申请加入"${club.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 创建成员信息
          const member = {
            id: this.data.userId,
            name: '我',
            avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };

          console.log('Joining club with member:', member);

          // 加入社团
          const success = joinClub(clubId, member);
          console.log('Join result:', success);

          if (success) {
            wx.showToast({
              title: '申请已提交',
              icon: 'success'
            });

            // 重新加载社团数据
            this.loadClubs();
          } else {
            wx.showToast({
              title: '申请失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 取消加入申请
  cancelJoinRequest(e) {
    // 在微信小程序中，catchtap 已经阻止了事件冒泡，不需要手动调用 stopPropagation

    const clubId = e.currentTarget.dataset.id;
    const club = this.data.clubs.find(c => c.id === clubId) ||
                 getClubById(clubId);

    if (!club) return;

    wx.showModal({
      title: '取消申请',
      content: `确定取消加入"${club.name}"的申请吗？`,
      success: (res) => {
        if (res.confirm) {
          // 获取社团详情
          const currentClub = getClubById(clubId);

          if (!currentClub || !currentClub.members) {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
            return;
          }

          // 移除当前用户的申请
          currentClub.members = currentClub.members.filter(m =>
            !(m.id === this.data.userId && m.status === 'pending')
          );

          // 更新社团
          const success = updateClub(clubId, currentClub);

          if (success) {
            wx.showToast({
              title: '已取消申请',
              icon: 'success'
            });

            // 重新加载社团数据
            this.loadClubs();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  },

  // 阻止事件冒泡的空函数
  preventBubble() {
    // 这个函数不需要做任何事情，只是用来阻止事件冒泡
    console.log('Preventing event bubble');
  }
})