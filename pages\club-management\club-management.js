// pages/club-management/club-management.js
import { getClubById, updateClub } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    clubId: null,
    club: null,
    loading: true,
    formData: {
      name: '',
      sportTypes: [],
      description: ''
    },
    formValid: false,
    sportTypes: [
      { name: '足球', selected: false },
      { name: '篮球', selected: false },
      { name: '排球', selected: false },
      { name: '羽毛球', selected: false },
      { name: '乒乓球', selected: false },
      { name: '网球', selected: false },
      { name: '游泳', selected: false },
      { name: '健身', selected: false },
      { name: '跑步', selected: false },
      { name: '瑜伽', selected: false },
      { name: '舞蹈', selected: false },
      { name: '武术', selected: false }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({
        clubId: options.id
      });
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '缺少社团ID',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载社团详情
  loadClubDetail() {
    wx.showLoading({
      title: '加载中...',
    });

    // 获取社团详情
    const club = getClubById(this.data.clubId);

    if (!club) {
      wx.hideLoading();
      wx.showToast({
        title: '社团不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 初始化表单数据
    const formData = {
      name: club.name,
      sportTypes: club.sportTypes || [],
      description: club.description || ''
    };

    // 初始化运动类型选中状态
    const sportTypes = this.data.sportTypes.map(item => {
      return {
        ...item,
        selected: formData.sportTypes.includes(item.name)
      };
    });

    this.setData({
      club,
      formData,
      sportTypes,
      loading: false
    });

    // 验证表单
    this.validateForm();

    wx.hideLoading();
  },

  // 社团名称输入事件
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
    this.validateForm();
  },

  // 运动类型选择事件
  toggleSportType(e) {
    const index = e.currentTarget.dataset.index;
    const sportTypes = this.data.sportTypes;

    // 切换选中状态
    sportTypes[index].selected = !sportTypes[index].selected;

    // 更新选中的运动类型
    const selectedSportTypes = sportTypes
      .filter(item => item.selected)
      .map(item => item.name);

    this.setData({
      sportTypes,
      'formData.sportTypes': selectedSportTypes
    });
    this.validateForm();
  },

  // 社团介绍输入事件
  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
    this.validateForm();
  },

  // 表单验证
  validateForm() {
    const { name, sportTypes, description } = this.data.formData;

    // 验证表单是否有效
    const formValid =
      name.trim() !== '' &&
      sportTypes.length > 0 &&
      description.trim() !== '';

    this.setData({
      formValid
    });
  },

  // 提交表单
  submitForm(e) {
    if (!this.data.formValid) {
      return;
    }

    const { name, sportTypes, description } = this.data.formData;

    wx.showLoading({
      title: '保存中...',
    });

    // 更新社团数据
    const updatedClub = {
      name,
      sportTypes,
      description
    };

    // 更新社团
    const success = updateClub(this.data.clubId, updatedClub);

    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 返回社团详情页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } else {
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
})