<!--pages/my-clubs/my-clubs.wxml-->
<view class="my-clubs-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">我的社团</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 社团类型选项卡 -->
    <view class="club-tabs">
      <view class="tab-header">
        <view class="tab-item {{currentTab === 'joined' ? 'active' : ''}}" bindtap="switchTab" data-tab="joined">已加入</view>
        <view class="tab-item {{currentTab === 'pending' ? 'active' : ''}}" bindtap="switchTab" data-tab="pending">待审核</view>
        <view class="tab-item {{currentTab === 'created' ? 'active' : ''}}" bindtap="switchTab" data-tab="created">我创建的</view>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 已加入社团列表 -->
    <scroll-view scroll-y="true" class="clubs-list" wx:if="{{!loading && currentTab === 'joined'}}">
      <!-- 空状态提示 -->
      <view class="empty-container" wx:if="{{joinedClubs.length === 0}}">
        <view class="empty-icon">🏆</view>
        <view class="empty-text">暂无已加入的社团</view>
        <view class="empty-subtext">浏览社团列表，加入感兴趣的运动社团</view>
        <button class="browse-button" bindtap="navigateToAllClubs">浏览社团</button>
      </view>

      <!-- 社团列表 -->
      <view class="club-item" wx:for="{{joinedClubs}}" wx:key="id">
        <!-- 社团信息 -->
        <view class="club-info" bindtap="navigateToClubDetail" data-id="{{item.id}}">
          <view class="club-logo-container">
            <image class="club-logo" src="{{item.logo}}" mode="aspectFill" wx:if="{{item.logo}}"></image>
            <view class="club-logo-placeholder" wx:else>社团</view>
            <view class="club-member-count">
              <text>成员：{{item.memberCount}}人</text>
            </view>
          </view>
          <view class="club-details">
            <view class="club-name-container">
              <view class="club-name">{{item.name}}</view>
              <view class="club-member-badge">成员</view>
            </view>
            <view class="club-org">所属单位：{{item.orgName}}</view>
            <view class="club-sports">
              <text wx:for="{{item.sportTypes}}" wx:for-item="sport" wx:key="*this" class="sport-tag">{{sport}}</text>
            </view>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="club-actions">
          <button class="enter-button" bindtap="navigateToClubDetail" data-id="{{item.id}}">进入社团</button>
        </view>
      </view>
    </scroll-view>

    <!-- 待审核社团列表 -->
    <scroll-view scroll-y="true" class="clubs-list" wx:if="{{!loading && currentTab === 'pending'}}">
      <!-- 空状态提示 -->
      <view class="empty-container" wx:if="{{pendingClubs.length === 0}}">
        <view class="empty-icon">⏳</view>
        <view class="empty-text">暂无待审核的社团</view>
        <view class="empty-subtext">浏览社团列表，申请加入感兴趣的运动社团</view>
        <button class="browse-button" bindtap="navigateToAllClubs">浏览社团</button>
      </view>

      <!-- 社团列表 -->
      <view class="club-item" wx:for="{{pendingClubs}}" wx:key="id">
        <!-- 社团信息 -->
        <view class="club-info">
          <view class="club-logo-container">
            <image class="club-logo" src="{{item.logo}}" mode="aspectFill" wx:if="{{item.logo}}"></image>
            <view class="club-logo-placeholder" wx:else>社团</view>
            <view class="club-member-count">
              <text>成员：{{item.memberCount}}人</text>
            </view>
          </view>
          <view class="club-details">
            <view class="club-name-container">
              <view class="club-name">{{item.name}}</view>
              <view class="club-pending-badge">审核中</view>
            </view>
            <view class="club-org">所属单位：{{item.orgName}}</view>
            <view class="club-sports">
              <text wx:for="{{item.sportTypes}}" wx:for-item="sport" wx:key="*this" class="sport-tag">{{sport}}</text>
            </view>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="club-actions">
          <button class="cancel-button" bindtap="cancelJoinRequest" data-id="{{item.id}}">取消申请</button>
        </view>
      </view>
    </scroll-view>

    <!-- 我创建的社团列表 -->
    <scroll-view scroll-y="true" class="clubs-list" wx:if="{{!loading && currentTab === 'created'}}">
      <!-- 空状态提示 -->
      <view class="empty-container" wx:if="{{createdClubs.length === 0}}">
        <view class="empty-icon">🏅</view>
        <view class="empty-text">暂无创建的社团</view>
        <view class="empty-subtext">创建一个新的运动社团，邀请同事一起运动</view>
        <button class="create-button" bindtap="navigateToCreateClub">创建社团</button>
      </view>

      <!-- 社团列表 -->
      <view class="club-item" wx:for="{{createdClubs}}" wx:key="id">
        <!-- 社团信息 -->
        <view class="club-info" bindtap="navigateToClubDetail" data-id="{{item.id}}">
          <view class="club-logo-container">
            <image class="club-logo" src="{{item.logo}}" mode="aspectFill" wx:if="{{item.logo}}"></image>
            <view class="club-logo-placeholder" wx:else>社团</view>
            <view class="club-member-count">
              <text>成员：{{item.memberCount}}人</text>
            </view>
          </view>
          <view class="club-details">
            <view class="club-name-container">
              <view class="club-name">{{item.name}}</view>
              <view class="club-creator-badge">创建者</view>
            </view>
            <view class="club-org">所属单位：{{item.orgName}}</view>
            <view class="club-sports">
              <text wx:for="{{item.sportTypes}}" wx:for-item="sport" wx:key="*this" class="sport-tag">{{sport}}</text>
            </view>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="club-actions">
          <button class="enter-button" bindtap="navigateToClubDetail" data-id="{{item.id}}">进入社团</button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 创建社团按钮 -->
  <view class="create-club-button" bindtap="navigateToCreateClub">
    <text>+</text>
  </view>
</view>
