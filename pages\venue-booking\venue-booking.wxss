/* pages/venue-booking/venue-booking.wxss */
page {
  background-color: #f5f5f5;
}

.booking-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background-color: #E74C3C;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
  box-sizing: content-box;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.nav-back {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: white;
}

.nav-placeholder {
  width: 80rpx;
}

/* 内容区域 */
.booking-scroll-view {
  flex: 1;
  height: calc(100vh - 180rpx - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  margin-top: calc(90rpx + env(safe-area-inset-top));
  padding-bottom: 180rpx;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 场馆图片 */
.venue-image-container {
  width: 100%;
  height: 400rpx;
  position: relative;
}

.venue-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.venue-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.5) 100%);
}

/* 图片右上角分享按钮 */
.image-share-button {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 120rpx !important;
  height: 60rpx !important;
  background-color: rgba(231, 76, 60, 0.9) !important;
  border-radius: 30rpx !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0;
  border: none !important;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  /* 修复按钮形状 */
  min-height: 0;
  line-height: 1;
  overflow: hidden;
  flex-direction: row;
}

/* 覆盖微信按钮默认样式 */
.image-share-button::after {
  border: none;
  border-radius: 30rpx;
}

.share-icon {
  width: 28rpx;
  height: 28rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/></svg>');
  background-size: 28rpx 28rpx;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 6rpx;
}

.share-text {
  font-size: 24rpx;
  color: white;
  line-height: 1;
}

.venue-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

/* 认证提示样式 */
.certification-notice {
  display: flex;
  align-items: center;
  background-color: rgba(231, 76, 60, 0.1);
  padding: 16rpx 20rpx;
  margin: 10rpx 20rpx;
  border-radius: 8rpx;
}

.notice-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #E74C3C;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 12rpx;
  font-style: italic;
}

.notice-text {
  font-size: 26rpx;
  color: #E74C3C;
  flex: 1;
}

.notice-action {
  font-size: 26rpx;
  color: #E74C3C;
  font-weight: bold;
  padding: 6rpx 16rpx;
  border: 1px solid #E74C3C;
  border-radius: 30rpx;
  margin-left: 10rpx;
}

/* 日期选择器 */
.date-selector {
  background-color: white;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.date-tabs {
  display: flex;
  padding: 0 20rpx;
  justify-content: space-between;
}

.date-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 18%;
  height: 100rpx;
  border: none;
  position: relative;
  box-sizing: border-box;
}

.date-tab.active {
  border: 3rpx solid #E74C3C;
  border-radius: 8rpx;
  background-color: rgba(231, 76, 60, 0.1);
}

.date-tab.active::after {
  content: "✓";
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.date-day {
  font-size: 26rpx;
  margin-bottom: 8rpx;
  color: #333;
}

.date-value {
  font-size: 26rpx;
  color: #666;
}

.date-tab.active .date-day,
.date-tab.active .date-value {
  color: #E74C3C;
}

.more-dates {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 18%;
  height: 100rpx;
  border: none;
  box-sizing: border-box;
}

.calendar-icon {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 8rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666666'%3E%3Cpath d='M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zM9 14H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zm-8 4H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.more-dates-text {
  font-size: 24rpx;
  color: #666;
}

/* 场馆选择 */
.hall-selector {
  display: flex;
  background-color: white;
  padding: 20rpx 30rpx 0 30rpx;
  position: relative;
  flex-wrap: nowrap;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hall-selector::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.hall-tab {
  width: auto;
  flex: 1;
  padding: 10rpx 0 20rpx 0;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  position: relative;
  box-sizing: border-box;
  white-space: normal;
  word-break: break-all;
  margin: 0 10rpx;
}

.hall-tab.active {
  color: #333;
  font-weight: bold;
}

.hall-indicator-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2rpx;
  background-color: #eee;
}

.active-hall-indicator {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #E74C3C;
  left: 0;
  right: 0;
  margin: 0 auto;
  display: none;
}

.hall-tab.active .active-hall-indicator {
  display: block;
}

/* 时间段选择 */
.time-slots-container {
  margin-top: 10rpx;
  background-color: white;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 800rpx;
}

.time-slots-grid {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 800rpx;
}

.no-slots-message {
  text-align: center;
  padding: 0;
  background-color: white;
  width: 100%;
  box-sizing: border-box;
  height: 800rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.no-slots-text {
  font-size: 34rpx;
  color: #999;
  font-weight: 300;
}

.court-headers {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: white;
  width: 100%;
  box-sizing: border-box;
}

.court-header {
  flex: 1;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.time-slot-row {
  display: flex;
  padding: 15rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  background-color: white;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.time-label {
  width: 90rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10rpx;
  position: relative;
}

/* 时间点和时间线样式 */
.time-label {
  position: relative;
  display: flex;
  align-items: center;
}

.time-label::after {
  content: "•";
  margin-left: 10rpx;
  color: #999;
  position: relative;
  z-index: 2;
}

/* 时间线样式 - 使用伪元素在每个时间点后添加垂直线 */
.time-slots-container {
  position: relative;
}

.time-slots-container::before {
  content: "";
  position: absolute;
  left: 90rpx;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background-color: #ddd;
  z-index: 1;
}

.court-slots {
  flex: 1;
  display: flex;
  justify-content: space-around;
  width: calc(100% - 90rpx);
}

.time-slot {
  flex: 1;
  height: 70rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 1rpx solid #e0e0e0;
  background-color: white;
  min-width: 120rpx;
}

.time-slot.unavailable {
  background-color: #f0f0f0;
  color: #999;
}

.time-slot.selected {
  border: 3rpx solid #E74C3C;
  border-radius: 8rpx;
  background-color: rgba(231, 76, 60, 0.1);
  position: relative;
}

.time-slot.selected::after {
  content: "✓";
  position: absolute;
  top: 0;
  right: 0;
  width: 30rpx;
  height: 30rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.time-slot.available {
  background-color: white;
}

.time-slot-price {
  font-size: 28rpx;
  color: #E74C3C;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.original-slot-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

.discounted-slot-price {
  font-size: 28rpx;
  color: #E74C3C;
  font-weight: bold;
}

.time-slot.selected .time-slot-price {
  color: #E74C3C;
}

/* 时间段折扣标签 */
.time-slot-discount {
  position: absolute;
  top: -16rpx;
  left: -10rpx;
  background-color: #FFEB3B;
  color: #E74C3C;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  font-weight: bold;
  transform: scale(0.9);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  z-index: 3;
}

/* 选择状态说明 */
.booking-status-legend {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  background-color: white;
  border-top: 1rpx solid #eee;
}

/* 固定在底部的状态说明 */
.fixed-booking-status-legend {
  position: fixed;
  bottom: calc(100rpx + env(safe-area-inset-bottom)); /* 底部操作栏的高度 + 安全区域 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  background-color: white;
  border-top: 1rpx solid #eee;
  z-index: 89; /* 低于底部栏的z-index */
}

/* 已选场次展示 - 固定在底部 */
.selected-slots-container {
  position: fixed;
  bottom: 180rpx; /* 底部操作栏的高度 + 额外空间 */
  left: 0;
  right: 0;
  background-color: white;
  padding: 15rpx 0;
  z-index: 94;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.selected-slots-scroll {
  width: 100%;
  white-space: nowrap;
}

.selected-slots-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.selected-slot-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-right: 20rpx;
  min-width: 220rpx;
  max-width: 300rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.selected-slot-info {
  flex: 1;
}

.selected-slot-time {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.selected-slot-court {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.selected-slot-price-info {
  display: flex;
  align-items: center;
}

.selected-slot-original-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 8rpx;
}

.selected-slot-final-price {
  font-size: 22rpx;
  color: #E74C3C;
  font-weight: bold;
}

.selected-slot-delete {
  width: 40rpx;
  height: 40rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 300;
  margin-left: 10rpx;
}

/* 底部空白区域，确保内容不被底部栏遮挡 */
.bottom-spacer {
  height: 450rpx; /* 底部操作栏 + 图例 + 已选场次卡片的高度 + 额外空间 */
}

.status-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
  font-size: 24rpx;
  color: #999;
}

.status-icon {
  width: 30rpx;
  height: 30rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  border: 1rpx solid #e0e0e0;
}

.status-icon.selected {
  border: 3rpx solid #4ECDC4;
  border-radius: 4rpx;
  background-color: rgba(78, 205, 196, 0.1);
}

.status-icon.unavailable {
  background-color: #f0f0f0;
}

.status-icon.available {
  background-color: white;
}

/* 底部栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 95;
  padding-bottom: env(safe-area-inset-bottom);
  height: 100rpx; /* 确保高度固定 */
}

.bottom-actions {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  height: 100rpx;
  box-sizing: border-box;
  width: 100%;
}

.price-container {
  width: 38%;
  margin-right: 5rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.price-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
  margin-right: 10rpx;
}

.price-icon::before {
  content: "";
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  background-color: transparent;
  border-radius: 50%;
  top: 5rpx;
  left: 5rpx;
}

.selected-count {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #e74c3c;
  color: white;
  font-size: 20rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.price-text {
  font-size: 26rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-left: 8rpx;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #e74c3c;
}

.original-price {
  color: #999;
  font-size: 22rpx;
  text-decoration: line-through;
  display: block;
  line-height: 1;
  margin-bottom: 2rpx;
}

.total-price-line {
  display: flex;
  align-items: center;
  white-space: nowrap;
  line-height: 1.2;
}

.action-buttons {
  display: flex;
  align-items: center;
  width: 62%;
  justify-content: center;
}

.confirm-button {
  height: 70rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-right: 0;
  border: none;
  font-weight: normal;
  padding: 0 30rpx;
  line-height: 1;
  width: 220rpx;
}

.confirm-button.disabled {
  background-color: #f0f0f0;
  color: #999;
}



/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 场馆规则弹窗 - 居中显示 */
.venue-rules-modal .modal-content {
  width: 90%;
  max-height: 80vh; /* 增加最大高度 */
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  position: relative;
  padding-bottom: 20rpx; /* 增加底部内边距 */
}

/* 订单确认弹窗 - 底部滑出 */
.order-confirm-modal {
  align-items: flex-end;
  justify-content: flex-end;
}

.order-confirm-modal .modal-content {
  width: 100%;
  max-height: 90vh;
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.order-confirm-modal .modal-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.order-confirm-modal .modal-body {
  padding: 20rpx 30rpx;
  max-height: 70vh;
}

.order-confirm-modal .modal-footer {
  padding: 20rpx 30rpx 40rpx;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  padding: 25rpx 30rpx 15rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: center; /* 居中标题 */
  position: relative;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加惯性滚动 */
  padding: 20rpx 0 30rpx;
  max-height: 50vh; /* 固定高度 */
  height: 50vh; /* 固定高度 */
  box-sizing: border-box;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
  border-top: none;
}

.agree-button, .pay-button {
  width: 100%;
  height: 80rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  border: none;
  font-weight: 500;
}

.pay-button {
  background-color: #E74C3C;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  position: relative;
}

/* 折扣标签样式 */
.discount-tag {
  position: absolute;
  top: -20rpx;
  right: 20rpx;
  background-color: #FFEB3B;
  color: #E74C3C;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 规则内容 */
.rules-content, .refund-rules {
  margin-bottom: 20rpx;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
}

.refund-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 20rpx 0 15rpx;
  color: #333;
  text-align: left;
}

.rule-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
  padding: 0;
  text-align: left;
  display: block;
  width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  box-sizing: border-box;
}

.rule-highlight {
  color: #E74C3C;
}

.rules-wrapper {
  padding: 0 30rpx;
  box-sizing: border-box;
  width: 100%;
}

/* 订单确认样式 */
.order-venue-info {
  display: flex;
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eee;
}

.order-date {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.order-venue-image {
  width: 120rpx;
  height: 90rpx;
  border-radius: 6rpx;
  margin-right: 20rpx;
}

.order-venue-details {
  flex: 1;
}

.order-venue-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.order-venue-hall {
  font-size: 24rpx;
  color: #666;
}

.order-slots {
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.order-slot-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 30%;
  margin: 0 1.5% 15rpx;
  padding: 15rpx 0;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  position: relative;
}

.order-slot-time {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.order-slot-court {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.order-slot-price {
  font-size: 24rpx;
  color: #E74C3C;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-original-price {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

.order-final-price {
  font-size: 24rpx;
  color: #E74C3C;
  font-weight: bold;
}

.order-slot-discount-tag {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  font-size: 18rpx;
  color: #E74C3C;
  background-color: #FFEB3B;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  font-weight: bold;
  z-index: 3;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.order-price-info {
  margin-bottom: 20rpx;
}

.order-price-row {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
}

.order-price-label {
  font-size: 26rpx;
  color: #666;
}

.order-price-value {
  font-size: 26rpx;
  color: #333;
}

.coupon-value {
  color: #999;
}

.discount-value {
  color: #E74C3C;
  font-weight: bold;
  display: inline-block;
}

.discount-name {
  color: #666;
  font-size: 24rpx;
  margin-left: 10rpx;
  display: inline-block;
}

.total-row {
  border-top: 1rpx solid #eee;
  padding-top: 15rpx;
  margin-top: 5rpx;
}

.total-row .order-price-label,
.total-row .order-price-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #E74C3C;
}

.order-rules-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
}

.order-rules-content {
  max-height: 200rpx;
  overflow-y: auto;
}

.order-rules-summary {
  margin-bottom: 20rpx;
}
