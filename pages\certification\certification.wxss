/* pages/certification/certification.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.certification-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-bottom: 30rpx;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  background-color: #fff;
  padding: 0 30rpx;
  position: relative;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-placeholder {
  width: 60rpx;
}

/* 页面滚动区域 */
.page-scroll {
  height: calc(100vh - 90rpx);
  width: 100%;
}

/* 表单容器 */
.form-container {
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title:before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 6rpx;
  height: 30rpx;
  background-color: #E74C3C;
  border-radius: 3rpx;
}

.section-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

/* 凭证示例 */
.cert-examples {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.example-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 单位信息 */
.org-info {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 上传凭证 */
.upload-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.upload-item {
  width: 200rpx;
  height: 200rpx;
  margin: 10rpx;
  position: relative;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.upload-button {
  width: 200rpx;
  height: 200rpx;
  margin: 10rpx;
  background-color: #f9f9f9;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 60rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.upload-tip {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 认证说明 */
.cert-rules {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.rule-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.rule-item:last-child {
  margin-bottom: 0;
}

/* 提交按钮 */
.submit-container {
  margin-top: 50rpx;
  padding: 0 30rpx;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin: 0;
  padding: 0;
}

.submit-button.disabled {
  background-color: #ccc;
}

/* 认证成功页面 */
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx;
  min-height: 80vh;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.success-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 50rpx;
  text-align: center;
}

.success-org, .success-type {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.success-note {
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
  padding: 15rpx 30rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  text-align: center;
}

.back-button {
  width: 80%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 50rpx;
}
