// pages/venue-detail/venue-detail.js
import * as api from '../../utils/api';

Page({
  data: {
    venueId: null,
    venue: null,
    loading: true,
    currentImageIndex: 0,
  },

  onLoad: function(options) {
    // 获取传递的场馆ID
    const venueId = parseInt(options.id);

    if (!venueId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      venueId
    });

    // 加载场馆详情
    this.loadVenueDetail(venueId);
  },

  // 加载场馆详情
  loadVenueDetail: function(venueId) {
    wx.showLoading({
      title: '加载中...',
    });

    api.getVenueById(venueId)
      .then(res => {
        wx.hideLoading();

        if (res.code === 0 && res.data) {
          // 处理距离显示，将米转换为公里
          const venue = res.data;
          if (venue.distance) {
            // 如果距离小于1000米，显示米，否则显示公里
            venue.distance = venue.distance < 1000
              ? venue.distance + '米'
              : (venue.distance / 1000).toFixed(1) + '公里';
          }

          this.setData({
            venue: venue,
            loading: false
          });

          // 设置页面标题
          wx.setNavigationBarTitle({
            title: res.data.name
          });
        } else {
          wx.showToast({
            title: '获取场馆详情失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取场馆详情失败', err);
        wx.showToast({
          title: '获取场馆详情失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      });
  },

  // 轮播图切换事件
  onSwiperChange: function(e) {
    this.setData({
      currentImageIndex: e.detail.current
    });
  },

  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const images = this.data.venue.images;

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 查看公告详情
  viewAnnouncementDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    const announcement = this.data.venue.announcements.find(a => a.id === id);

    if (announcement) {
      wx.showModal({
        title: announcement.title,
        content: '公告详情功能开发中',
        showCancel: false,
        confirmText: '确定'
      });
    }
  },



  // 查看单馆详情
  viewSubVenueDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    const subVenue = this.data.venue.subVenues.find(sv => sv.id === id);

    if (subVenue) {
      // 这里可以跳转到单馆详情页，目前使用弹窗展示
      wx.showModal({
        title: subVenue.name,
        content: `营业时间: ${subVenue.openTime}\n价格: ${subVenue.price}\n设施: ${subVenue.facilities.join(', ')}\n状态: ${subVenue.status}`,
        showCancel: false,
        confirmText: '确定'
      });

      // 未来可以实现跳转到单馆详情页
      // wx.navigateTo({
      //   url: `/pages/sub-venue-detail/sub-venue-detail?id=${subVenue.id}&venueId=${this.data.venueId}`
      // });
    }
  },

  // 拨打电话
  makePhoneCall: function() {
    const { contact } = this.data.venue;

    if (contact) {
      wx.makePhoneCall({
        phoneNumber: contact,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败', err);
        }
      });
    }
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 预览图片
  previewImage: function(e) {
    const { index } = e.currentTarget.dataset;
    const { venue } = this.data;

    wx.previewImage({
      current: venue.images[index],
      urls: venue.images
    });
  },

  // 选择运动类型
  selectSportType: function(e) {
    const sportType = e.currentTarget.dataset.type;

    // 跳转到场地预定页面
    wx.navigateTo({
      url: `/pages/venue-booking/venue-booking?venueId=${this.data.venueId}&sportType=${sportType}`
    });
  },

  // 打开地图导航
  openLocation: function() {
    const { venue } = this.data;

    if (venue && venue.latitude && venue.longitude) {
      // 使用微信内置地图查看位置并提供导航功能
      wx.openLocation({
        latitude: venue.latitude,
        longitude: venue.longitude,
        name: venue.name,
        address: venue.address,
        scale: 18
      });
    } else {
      wx.showToast({
        title: '无法获取位置信息',
        icon: 'none'
      });
    }
  }
})
