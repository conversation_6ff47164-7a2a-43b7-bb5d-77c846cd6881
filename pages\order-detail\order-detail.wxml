<!--pages/order-detail/order-detail.wxml-->
<view class="order-detail-container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <block wx:if="{{!loading && order}}">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="navigateBack">
        <view class="back-icon">←</view>
      </view>
      <view class="nav-title">订单详情</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 固定内容区域 -->
    <scroll-view class="order-content" scroll-y="true">
      <!-- 订单状态 -->
      <view class="order-status {{order.status}}">
        <view class="status-icon">
          <view class="icon-circle">◷</view>
        </view>
        <view class="status-text">{{order.statusText}}</view>
      </view>

      <!-- 场馆信息 -->
      <view class="venue-info-card">
        <image src="{{order.venue.imageUrl}}" mode="aspectFill" class="venue-image"></image>
        <view class="venue-details">
          <view class="venue-name">{{order.venue.fullName}}</view>
          <view class="venue-hall">{{order.venue.fullHall}}</view>
        </view>
      </view>

      <!-- 价格信息 -->
      <view class="price-info">
        <view class="price-item">
          <view class="price-label">商品总价</view>
          <view class="price-value">¥{{order.price.total}}</view>
        </view>

        <view class="price-item">
          <view class="price-label">共优惠</view>
          <view class="price-value discount">-¥{{order.price.discount}}</view>
        </view>

        <view class="price-item total">
          <view class="price-label">实付金额</view>
          <view class="price-value">¥{{order.price.final}}</view>
        </view>
      </view>

      <!-- 订场场次 -->
      <view class="booking-sessions">
        <view class="booking-header">
          <view class="section-title">订场场次</view>
          <view class="booking-explanation" bindtap="showBookingExplanation">
            <text class="explanation-icon">ⓘ</text> 订场说明
          </view>
        </view>

        <view class="session-date">{{order.booking.date}} 共{{order.booking.totalSessions}}场</view>

        <view class="session-list">
          <view
            wx:for="{{order.booking.sessions}}"
            wx:key="time"
            class="session-item">
            <view class="session-venue">
              {{order.venue.name}} - {{order.venue.hall}} - {{item.area}}
              <text class="session-status">{{item.status}}</text>
            </view>
            <view class="session-time">{{order.booking.date}} {{order.booking.day}} {{item.time}}</view>

            <!-- 参与者头像 -->
            <view class="participants">
              <view
                wx:for="{{participants}}"
                wx:key="id"
                wx:for-item="participant"
                class="participant-avatar">
                <image src="{{participant.avatar}}" mode="aspectFill"></image>
                <text class="participant-name">{{participant.isMe ? '(我)' : ''}}{{participant.name}}</text>
              </view>

              <!-- 空位占位符 -->
              <view
                wx:for="{{[1,2,3,4].slice(0, 5 - participants.length)}}"
                wx:key="*this"
                class="participant-placeholder">
                <view class="placeholder-circle">?</view>
              </view>
            </view>

            <!-- 邀请好友按钮 -->
            <button class="invite-button" bindtap="inviteFriends">邀请好友</button>
          </view>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="order-info">
        <view class="section-title">订单信息</view>

        <view class="info-item">
          <view class="info-label">订单编号</view>
          <view class="info-value">{{order.orderInfo.orderNumber}}</view>
        </view>

        <view class="info-item">
          <view class="info-label">下单用户</view>
          <view class="info-value">{{order.orderInfo.user}}</view>
        </view>

        <view class="info-item">
          <view class="info-label">手机号码</view>
          <view class="info-value">{{order.orderInfo.phone}}</view>
        </view>

        <view class="info-item">
          <view class="info-label">下单时间</view>
          <view class="info-value">{{order.orderInfo.orderTime}}</view>
        </view>

        <view class="info-item">
          <view class="info-label">支付时间</view>
          <view class="info-value">{{order.orderInfo.payTime}}</view>
        </view>

        <view class="info-item">
          <view class="info-label">支付方式</view>
          <view class="info-value">{{order.orderInfo.payMethod}}</view>
        </view>
      </view>

      <!-- 底部提示 -->
      <view class="bottom-tip">
        如果订单异常，请尝试下拉刷新
      </view>

      <!-- 底部空白区域，为底部操作栏留出空间 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="more-button-container">
        <view class="more-button" bindtap="toggleMore">更多</view>
        <!-- 更多选项气泡 -->
        <view class="more-options-bubble {{showMoreOptions ? 'show' : ''}}" wx:if="{{showMoreOptions}}">
          <view class="bubble-arrow"></view>
          <view class="bubble-option" bindtap="contactService">联系客服</view>
          <view class="bubble-option" bindtap="backToHome">返回首页</view>
        </view>
      </view>
      <view class="scan-button" bindtap="scanToEnter">扫码签到</view>
    </view>

    <!-- 签到弹窗 -->
    <view class="sign-in-modal" wx:if="{{showSignInModal}}">
      <view class="sign-in-content">
        <view class="sign-in-header">
          <view class="sign-in-title">场地签到</view>
          <view class="sign-in-close" bindtap="closeSignInModal">×</view>
        </view>
        <view class="sign-in-body">
          <view class="sign-in-venue">
            <view class="sign-in-venue-name">{{order.venue.fullName || order.venue.name}}</view>
            <view class="sign-in-venue-hall">{{order.venue.fullHall || order.venue.hall}} - {{currentSession.area}}</view>
          </view>

          <view class="sign-in-time">
            <view class="sign-in-date">{{order.booking.date}} {{order.booking.day}}</view>
            <view class="sign-in-session">{{currentSession.time}}</view>
          </view>

          <view class="sign-in-status">
            <view class="sign-in-status-text">
              {{currentSession.status === '已完成' ? '该场次已签到' : '请确认以上信息无误'}}
            </view>
          </view>

          <view class="sign-in-button-container">
            <button
              class="sign-in-button {{!canSignIn || currentSession.status === '已完成' ? 'disabled' : ''}}"
              bindtap="confirmSignIn"
              disabled="{{!canSignIn || currentSession.status === '已完成'}}">
              {{currentSession.status === '已完成' ? '已签到' : canSignIn ? '确认签到' : '确认签到(' + countDown + 's)'}}
            </button>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>
