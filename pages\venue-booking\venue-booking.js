// pages/venue-booking/venue-booking.js
import { venues } from '../../mock/venues.js';
import { saveOrder, getCertifications } from '../../utils/storage.js';

Page({
  data: {
    venueId: null,
    sportType: null,
    venue: null,
    loading: true,

    // 日期选择
    dateOptions: [],
    selectedDate: '',

    // 场馆选择
    selectedHallId: null,
    selectedHall: null,

    // 时间段选择
    timeSlots: [],
    hasAvailableSlots: true,
    noSlotsMessage: '',

    // 已选场次
    selectedSlots: [],
    totalPrice: 0,

    // 弹窗控制
    showRulesModal: false,
    showOrderModal: false
  },

  onLoad: function(options) {
    // 获取传递的场馆ID和运动类型
    const venueId = parseInt(options.venueId);
    const sportType = options.sportType || '';

    if (!venueId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      venueId,
      sportType
    });

    // 检查用户是否有企事业单位认证
    this.checkCertification();

    // 加载场馆详情
    this.loadVenueDetail(venueId, sportType);

    // 初始化日期选项
    this.initDateOptions();

    // 检测平台
    this.checkPlatform();
  },

  // 检测平台
  checkPlatform: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const isIOS = systemInfo.platform === 'ios';
      this.setData({
        isIOS: isIOS
      });
      console.log('当前平台:', isIOS ? 'iOS' : '非iOS');
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  // 检查用户是否有企事业单位认证
  checkCertification: function() {
    // 获取用户的认证记录
    const certifications = getCertifications();

    // 如果有认证记录，则应用5折优惠
    if (certifications && certifications.length > 0) {
      this.setData({
        hasCertification: true,
        discount: 0.5, // 5折
        discountName: '工会优惠'
      });
    } else {
      this.setData({
        hasCertification: false,
        discount: 1, // 无折扣
        discountName: ''
      });
    }
  },

  // 初始化日期选项
  initDateOptions: function() {
    const days = ['今天', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const dateOptions = [];

    const today = new Date();

    // 生成未来4天的日期选项
    for (let i = 0; i < 4; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      const month = date.getMonth() + 1;
      const day = date.getDate();
      const dayOfWeek = date.getDay();

      let dayName = days[0]; // 默认为"今天"
      if (i > 0) {
        dayName = days[dayOfWeek === 0 ? 7 : dayOfWeek]; // 周日是0，转为7
      }

      dateOptions.push({
        date: `${date.getFullYear()}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`,
        day: dayName,
        value: `${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
      });
    }

    this.setData({
      dateOptions,
      selectedDate: dateOptions[0].date // 默认选中今天
    });
  },

  // 加载场馆详情
  loadVenueDetail: function(venueId, sportType) {
    wx.showLoading({
      title: '加载中...',
    });

    // 从mock数据中获取场馆信息
    const venue = venues.find(v => v.id == venueId);

    if (!venue) {
      wx.showToast({
        title: '未找到场馆信息',
        icon: 'none'
      });
      wx.hideLoading();
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 处理场馆数据，添加预订相关信息
    const processedVenue = this.processVenueData(venue, sportType);

    // 设置默认选中的场馆
    const defaultHall = processedVenue.bookingHalls && processedVenue.bookingHalls.length > 0
      ? processedVenue.bookingHalls[0]
      : null;

    this.setData({
      venue: processedVenue,
      loading: false,
      selectedHallId: defaultHall ? defaultHall.id : null,
      selectedHall: defaultHall
    });

    // 生成时间段数据
    this.generateTimeSlots();

    wx.hideLoading();
  },

  // 处理场馆数据，添加预订相关信息
  processVenueData: function(venue, sportType) {
    // 根据运动类型筛选相关场馆
    let filteredSubVenues = [];

    // 如果指定了运动类型，根据运动类型筛选
    if (sportType && sportType !== '') {
      // 根据运动类型映射到相应的场馆名称
      const sportTypeMapping = {
        '羽毛球': ['羽毛球', '羽毛球馆'],
        '乒乓球': ['乒乓球', '乒乓球馆'],
        '网球': ['网球', '网球场'],
        '足球': ['足球', '足球场'],
        '篮球': ['篮球', '篮球场']
      };

      // 获取当前运动类型对应的关键词
      const keywords = sportTypeMapping[sportType] || [sportType];

      // 筛选包含关键词的场馆
      filteredSubVenues = venue.subVenues.filter(sv =>
        keywords.some(keyword => sv.name.includes(keyword))
      );
    } else {
      // 如果没有指定运动类型，使用所有场馆
      filteredSubVenues = venue.subVenues;
    }

    // 如果没有找到相关场馆，返回空数据
    if (filteredSubVenues.length === 0) {
      return {
        ...venue,
        bookingHalls: []
      };
    }

    // 查找带有片区的场馆
    let selectedHall = filteredSubVenues.find(hall => hall.areas && hall.areas.length > 0);

    // 如果没有找到带有片区的场馆，使用第一个场馆并创建默认片区
    if (!selectedHall && filteredSubVenues.length > 0) {
      selectedHall = filteredSubVenues[0];
    }

    // 如果有片区数据，直接使用
    if (selectedHall && selectedHall.areas) {
      return {
        ...venue,
        bookingHalls: selectedHall.areas
      };
    }

    // 如果没有片区数据，使用单馆作为片区
    // 将每个单馆转换为一个片区
    const defaultAreas = filteredSubVenues.map(hall => {
      // 为每个场馆添加场地信息
      const courts = [];
      // 根据场馆类型决定场地数量和命名
      let courtCount = 2; // 默认2个场地
      let courtNamePrefix = '号场'; // 默认命名

      if (hall.name.includes('羽毛球')) {
        courtCount = 3;
      } else if (hall.name.includes('乒乓球')) {
        courtCount = 4;
        courtNamePrefix = '号台';
      } else if (hall.name.includes('足球')) {
        courtCount = 3;
        // 足球场使用特殊命名
        courts.push({ id: `${hall.id}_1`, name: '5人制' });
        courts.push({ id: `${hall.id}_2`, name: '7人制' });
        courts.push({ id: `${hall.id}_3`, name: '11人制' });
        courtCount = 0; // 已经添加了场地，不需要再循环添加
      }

      // 添加常规命名的场地
      for (let i = 1; i <= courtCount; i++) {
        courts.push({
          id: `${hall.id}_${i}`,
          name: `${i}${courtNamePrefix}`
        });
      }

      return {
        id: hall.id,
        name: hall.name,
        imageUrl: hall.imageUrl || venue.imageUrl,
        courts: courts
      };
    });

    return {
      ...venue,
      bookingHalls: defaultAreas
    };
  },

  // 生成时间段数据
  generateTimeSlots: function() {
    const { selectedHall, selectedDate } = this.data;

    if (!selectedHall) return;

    // 生成时间段，从17:00到22:00，每小时一个时间段
    const timeSlots = [];
    const startHour = 17;
    const endHour = 22;

    // 获取当前时间信息
    const today = new Date();
    const selectedDateObj = new Date(selectedDate);
    const isToday = selectedDateObj.toDateString() === today.toDateString();
    const currentHour = today.getHours();

    // 标记是否有可用场次
    let hasAvailableSlots = false;

    for (let hour = startHour; hour <= endHour; hour++) {
      // 如果是今天且时间已过，则跳过该时间段（不显示）
      if (isToday && hour <= currentHour) {
        continue; // 跳过已过期的时间段
      }

      const time = `${hour}:00`;
      const courts = [];

      // 为每个场地生成状态和价格
      selectedHall.courts.forEach(court => {
        // 根据日期和时间生成状态
        // 实际项目中应该从服务器获取
        let status = 'available';

        // 随机生成一些不可用的时间段
        const random = Math.random();
        if (random < 0.3) {
          status = 'unavailable';
        } else {
          // 如果至少有一个场地可用，标记为有可用场次
          hasAvailableSlots = true;
        }

        // 默认价格
        const originalPrice = 60;

        // 如果用户有认证，计算折扣价格
        const discountedPrice = this.data.hasCertification ? Math.round(originalPrice * this.data.discount) : originalPrice;

        courts.push({
          id: court.id,
          name: court.name,
          status: status,
          price: originalPrice, // 显示原价
          discountedPrice: discountedPrice, // 折扣后价格
          selected: false
        });
      });

      timeSlots.push({
        time,
        courts
      });
    }

    this.setData({
      timeSlots,
      hasAvailableSlots: hasAvailableSlots || timeSlots.length > 0,
      noSlotsMessage: isToday ? '今日已无可定场次' : '暂无可预订场次'
    });
  },

  // 选择日期
  selectDate: function(e) {
    const date = e.currentTarget.dataset.date;

    this.setData({
      selectedDate: date
    });

    // 重新生成时间段数据
    this.generateTimeSlots();

    // 清空已选场次
    this.setData({
      selectedSlots: [],
      totalPrice: 0
    });
  },

  // 显示更多日期
  showMoreDates: function() {
    wx.showToast({
      title: '日期选择功能开发中',
      icon: 'none'
    });
  },

  // 选择场馆
  selectHall: function(e) {
    const hallId = e.currentTarget.dataset.id;
    const hall = this.data.venue.bookingHalls.find(h => h.id === hallId);

    if (hall) {
      this.setData({
        selectedHallId: hallId,
        selectedHall: hall
      });

      // 重新生成时间段数据
      this.generateTimeSlots();

      // 清空已选场次
      this.setData({
        selectedSlots: [],
        totalPrice: 0
      });
    }
  },

  // 选择/取消选择时间段
  toggleTimeSlot: function(e) {
    const { time, courtId, price, status } = e.currentTarget.dataset;

    // 如果状态是不可用，则不处理
    if (status === 'unavailable') return;

    // 找到对应的时间段和场地
    const timeSlots = [...this.data.timeSlots];
    const timeSlotIndex = timeSlots.findIndex(ts => ts.time === time);

    if (timeSlotIndex === -1) return;

    const courtIndex = timeSlots[timeSlotIndex].courts.findIndex(c => c.id === courtId);

    if (courtIndex === -1) return;

    // 切换选中状态
    const isSelected = timeSlots[timeSlotIndex].courts[courtIndex].selected;
    timeSlots[timeSlotIndex].courts[courtIndex].selected = !isSelected;

    // 更新已选场次
    let selectedSlots = [...this.data.selectedSlots];
    const slotId = `${time}_${courtId}`;

    if (isSelected) {
      // 取消选中
      selectedSlots = selectedSlots.filter(slot => slot.id !== slotId);
    } else {
      // 选中
      const court = this.data.selectedHall.courts.find(c => c.id === courtId);

      // 计算结束时间
      const endTime = this.calculateEndTime(time);

      // 找到对应的时间段和场地，获取折扣价格
      const timeSlot = timeSlots.find(ts => ts.time === time);
      const courtObj = timeSlot ? timeSlot.courts.find(c => c.id === courtId) : null;

      // 使用折扣价格（如果有）
      const finalPrice = courtObj && this.data.hasCertification ?
        courtObj.discountedPrice : parseInt(price);

      selectedSlots.push({
        id: slotId,
        time,
        endTime,
        courtId,
        courtName: court ? court.name : '',
        price: parseInt(price),
        finalPrice: finalPrice,
        hasDiscount: this.data.hasCertification
      });
    }

    // 计算原价
    const originalPrice = selectedSlots.reduce((sum, slot) => sum + slot.price, 0);

    // 计算最终价格（已经包含了折扣）
    const totalPrice = selectedSlots.reduce((sum, slot) => sum + (slot.finalPrice || slot.price), 0);

    this.setData({
      timeSlots,
      selectedSlots,
      originalPrice,
      totalPrice
    });
  },

  // 计算结束时间
  calculateEndTime: function(startTime) {
    const [hour, minute] = startTime.split(':').map(Number);
    let endHour = hour;
    let endMinute = minute + 20; // 默认时间段为20分钟

    if (endMinute >= 60) {
      endHour += 1;
      endMinute -= 60;
    }

    return `${endHour}:${endMinute < 10 ? '0' + endMinute : endMinute}`;
  },

  // 移除已选场次
  removeTimeSlot: function(e) {
    const slotId = e.currentTarget.dataset.id;

    // 从已选场次中移除
    const selectedSlots = this.data.selectedSlots.filter(slot => slot.id !== slotId);

    // 更新时间段选中状态
    const timeSlots = [...this.data.timeSlots];

    if (slotId) {
      const [time, courtId] = slotId.split('_');
      const timeSlotIndex = timeSlots.findIndex(ts => ts.time === time);

      if (timeSlotIndex !== -1) {
        const courtIndex = timeSlots[timeSlotIndex].courts.findIndex(c => c.id === courtId);

        if (courtIndex !== -1) {
          timeSlots[timeSlotIndex].courts[courtIndex].selected = false;
        }
      }
    }

    // 计算原价
    const originalPrice = selectedSlots.reduce((sum, slot) => sum + slot.price, 0);

    // 计算最终价格（已经包含了折扣）
    const totalPrice = selectedSlots.reduce((sum, slot) => sum + (slot.finalPrice || slot.price), 0);

    this.setData({
      selectedSlots,
      timeSlots,
      originalPrice,
      totalPrice
    });
  },

  // 确认预订
  confirmBooking: function() {
    // 检查是否有可用场次和已选场次
    if (this.data.timeSlots.length === 0 || this.data.selectedSlots.length === 0) {
      return;
    }

    // 显示场馆规则弹窗
    // 在iOS上，使用更长的延迟，确保UI渲染正确
    const delay = this.data.isIOS ? 100 : 50;
    setTimeout(() => {
      this.setData({
        showRulesModal: true
      });
    }, delay);
  },

  // 关闭规则弹窗
  closeRulesModal: function() {
    this.setData({
      showRulesModal: false
    });
  },

  // 同意规则
  agreeRules: function() {
    // 先关闭规则弹窗，然后延迟显示订单弹窗，避免iOS上的渲染问题
    this.setData({
      showRulesModal: false
    });

    // 在iOS上使用更长的延迟
    const delay = this.data.isIOS ? 500 : 300;
    setTimeout(() => {
      this.setData({
        showOrderModal: true
      });
    }, delay);
  },

  // 关闭订单弹窗
  closeOrderModal: function() {
    this.setData({
      showOrderModal: false
    });
  },

  // 处理支付
  processPayment: function() {
    // 模拟支付成功
    wx.showLoading({
      title: '支付处理中...',
    });

    // 获取当前日期和星期几
    const selectedDate = this.data.selectedDate;
    const dateObj = new Date(selectedDate);
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekDay = weekDays[dateObj.getDay()];

    // 格式化日期为 YYYY.MM.DD 格式
    const formattedDate = selectedDate.replace(/-/g, '.');

    // 创建订单数据
    const orderData = {
      id: 'ORDER_' + Date.now(),
      status: 'processing',
      statusText: '订单进行中',
      venue: {
        id: this.data.venue.id,
        name: this.data.venue.name,
        fullName: this.data.venue.name,
        hall: this.data.selectedHall.name,
        fullHall: this.data.selectedHall.name,
        imageUrl: this.data.selectedHall.imageUrl || this.data.venue.imageUrl
      },
      booking: {
        date: formattedDate,
        day: weekDay,
        totalSessions: this.data.selectedSlots.length,
        sessions: this.data.selectedSlots.map(slot => ({
          time: `${slot.time}-${slot.endTime}`,
          area: slot.courtName,
          status: '未开始'
        }))
      },
      orderInfo: {
        orderNumber: 'WZORDER_' + Date.now(),
        user: '用户' + Math.floor(Math.random() * 1000), // 模拟用户名
        phone: '138****' + Math.floor(Math.random() * 10000), // 模拟手机号
        orderTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        payTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        payMethod: '微信支付'
      },
      price: {
        total: this.data.totalPrice.toString(),
        discount: "0",
        final: this.data.totalPrice.toString(),
        discountName: ""
      },
      participants: [
        {
          id: 1,
          name: '我',
          avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
          isMe: true
        }
      ]
    };

    // 保存订单到本地存储
    saveOrder(orderData);

    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '支付成功',
        icon: 'success',
        duration: 2000
      });

      // 关闭弹窗
      this.setData({
        showOrderModal: false
      });

      // 延迟跳转到订单详情页
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/order-detail/order-detail?orderId=${orderData.id}`
        });
      }, 2000);
    }, 1500);
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 跳转到企事业单位认证列表
  navigateToCertification: function() {
    wx.navigateTo({
      url: '/pages/organizations/organizations'
    });
  },

  // 分享
  onShareAppMessage: function() {
    return {
      title: `${this.data.venue.name} - ${this.data.sportType}场地预订`,
      path: `/pages/venue-booking/venue-booking?venueId=${this.data.venueId}&sportType=${this.data.sportType}`
    };
  }
});
