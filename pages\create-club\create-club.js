// pages/create-club/create-club.js
import { createClub } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    formData: {
      name: '',
      orgId: '',
      orgName: '',
      sportTypes: [],
      description: '',
      logo: '' // 移除默认logo
    },
    formValid: false,
    orgIndex: 0,
    organizations: [],
    certifiedOrganizations: [], // 已认证的单位
    sportSearchText: '', // 运动类型搜索文本
    showSportTypeDropdown: false, // 是否显示运动类型下拉框
    sportTypes: [
      { name: '足球', selected: false },
      { name: '篮球', selected: false },
      { name: '排球', selected: false },
      { name: '羽毛球', selected: false },
      { name: '乒乓球', selected: false },
      { name: '网球', selected: false },
      { name: '游泳', selected: false },
      { name: '健身', selected: false },
      { name: '跑步', selected: false },
      { name: '瑜伽', selected: false },
      { name: '舞蹈', selected: false },
      { name: '武术', selected: false },
      { name: '高尔夫', selected: false },
      { name: '攀岩', selected: false },
      { name: '滑雪', selected: false },
      { name: '滑冰', selected: false },
      { name: '自行车', selected: false },
      { name: '棒球', selected: false },
      { name: '击剑', selected: false },
      { name: '射箭', selected: false },
      { name: '拳击', selected: false },
      { name: '柔道', selected: false },
      { name: '跆拳道', selected: false },
      { name: '空手道', selected: false },
      { name: '铁人三项', selected: false },
      { name: '马拉松', selected: false },
      { name: '飞盘', selected: false },
      { name: '橄榄球', selected: false }
    ],
    filteredSportTypes: [], // 过滤后的运动类型
    userId: 'user_123' // 使用固定ID以便与数据中的创建者ID匹配
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 加载企事业单位数据
    this.loadOrganizations();

    // 初始化过滤后的运动类型
    this.initFilteredSportTypes();

    // 添加点击事件监听，用于关闭下拉框
    this.setupClickListener();
  },

  // 设置点击事件监听
  setupClickListener() {
    // 在微信小程序中，我们可以通过页面的tap事件来处理
    // 这里不需要额外设置，我们会在页面的点击事件中处理
  },

  // 页面点击事件
  onPageTap(e) {
    // 检查是否点击了下拉框区域
    const path = e.target.dataset.area;

    // 如果点击的不是特定区域，则关闭下拉框
    if (path !== 'dropdown' && this.data.showSportTypeDropdown) {
      this.setData({
        showSportTypeDropdown: false
      });
    }
  },

  // 初始化过滤后的运动类型
  initFilteredSportTypes() {
    const sportTypes = this.data.sportTypes.map((item, index) => ({
      ...item,
      originalIndex: index // 保存原始索引
    }));

    this.setData({
      filteredSportTypes: sportTypes
    });
  },

  // 加载企事业单位数据
  loadOrganizations() {
    // 获取用户认证的单位
    const { getCertifications } = require('../../utils/storage');
    const certifications = getCertifications();

    // 如果没有认证记录，显示提示
    if (certifications.length === 0) {
      wx.showModal({
        title: '提示',
        content: '您还没有认证任何单位，请先进行单位认证',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 获取已认证单位的详细信息
    const certifiedOrgs = [];

    // 筛选已通过认证的单位
    const approvedCertifications = certifications.filter(cert => cert.status === 'approved');

    // 如果没有已通过认证的单位，显示提示
    if (approvedCertifications.length === 0) {
      wx.showModal({
        title: '提示',
        content: '您的单位认证申请正在审核中，请等待审核通过后再创建社团',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 将认证信息转换为组织信息
    approvedCertifications.forEach(cert => {
      certifiedOrgs.push({
        id: cert.orgId,
        name: cert.orgName,
        logo: cert.orgLogo || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
      });
    });

    this.setData({
      organizations: certifiedOrgs,
      certifiedOrganizations: certifiedOrgs
    });

    // 如果只有一个认证单位，自动选择
    if (certifiedOrgs.length === 1) {
      this.setData({
        'formData.orgId': certifiedOrgs[0].id,
        'formData.orgName': certifiedOrgs[0].name,
        orgIndex: 0
      });
      this.validateForm();
    }
  },

  // 社团名称输入事件
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
    this.validateForm();
  },

  // 企事业单位选择事件
  onOrgChange(e) {
    const index = e.detail.value;
    const org = this.data.organizations[index];

    this.setData({
      orgIndex: index,
      'formData.orgId': org.id,
      'formData.orgName': org.name
    });
    this.validateForm();
  },

  // 运动类型搜索输入事件
  onSportSearchInput(e) {
    const searchText = e.detail.value.toLowerCase();

    this.setData({
      sportSearchText: searchText,
      showSportTypeDropdown: true // 输入时显示下拉框
    });

    this.filterSportTypes(searchText);
  },

  // 显示运动类型下拉框
  showSportTypeDropdown() {
    this.setData({
      showSportTypeDropdown: true
    });
  },

  // 切换运动类型下拉框显示状态
  toggleSportTypeDropdown() {
    this.setData({
      showSportTypeDropdown: !this.data.showSportTypeDropdown
    });
  },

  // 关闭下拉框
  closeDropdown() {
    this.setData({
      showSportTypeDropdown: false
    });
  },

  // 阻止事件冒泡（在微信小程序中，catchtap已经处理了冒泡，这个方法只是为了保持代码结构）
  stopPropagation() {
    // 微信小程序中不需要显式阻止冒泡
    return;
  },

  // 过滤运动类型
  filterSportTypes(searchText) {
    if (!searchText) {
      // 如果搜索文本为空，显示所有运动类型
      const sportTypes = this.data.sportTypes.map((item, index) => ({
        ...item,
        originalIndex: index
      }));

      this.setData({
        filteredSportTypes: sportTypes
      });
      return;
    }

    // 根据搜索文本过滤运动类型
    const filteredSportTypes = this.data.sportTypes
      .map((item, index) => ({
        ...item,
        originalIndex: index
      }))
      .filter(item => item.name.toLowerCase().includes(searchText));

    this.setData({
      filteredSportTypes
    });
  },

  // 运动类型选择事件
  toggleSportType(e) {
    // 微信小程序中不需要显式阻止冒泡，catchtap已经处理了

    const index = e.currentTarget.dataset.index;
    const sportTypes = this.data.sportTypes;

    // 切换选中状态
    sportTypes[index].selected = !sportTypes[index].selected;

    // 更新选中的运动类型
    const selectedSportTypes = sportTypes
      .filter(item => item.selected)
      .map(item => item.name);

    // 更新选择状态，不关闭下拉框
    this.setData({
      sportTypes,
      'formData.sportTypes': selectedSportTypes
    });

    // 更新过滤后的运动类型
    this.filterSportTypes(this.data.sportSearchText);

    this.validateForm();

    // 不关闭下拉框，允许多选
  },

  // 移除已选择的运动类型
  removeSportType(e) {
    // 微信小程序中不需要显式阻止冒泡，catchtap已经处理了

    const sportToRemove = e.currentTarget.dataset.sport;
    const sportTypes = this.data.sportTypes;

    // 找到要移除的运动类型索引
    const index = sportTypes.findIndex(item => item.name === sportToRemove);

    if (index !== -1) {
      // 更新选中状态
      sportTypes[index].selected = false;

      // 更新选中的运动类型
      const selectedSportTypes = sportTypes
        .filter(item => item.selected)
        .map(item => item.name);

      // 更新数据
      this.setData({
        sportTypes,
        'formData.sportTypes': selectedSportTypes
      });

      // 更新过滤后的运动类型
      this.filterSportTypes(this.data.sportSearchText);

      this.validateForm();
    }
  },

  // 社团介绍输入事件
  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
    this.validateForm();
  },

  // 表单验证
  validateForm() {
    const { name, orgId, sportTypes, description } = this.data.formData;

    // 验证表单是否有效
    const formValid =
      name.trim() !== '' &&
      orgId !== '' &&
      sportTypes.length > 0 &&
      description.trim() !== '';

    this.setData({
      formValid
    });
  },

  // 提交按钮点击事件
  onSubmitButtonTap() {
    console.log('提交按钮被点击');
    this.submitForm();
  },

  // 提交表单
  submitForm(e) {
    console.log('提交表单函数被调用');

    if (!this.data.formValid) {
      console.log('表单验证未通过');
      return;
    }

    const { name, orgId, orgName, sportTypes, description, logo } = this.data.formData;

    // 创建社团数据
    const clubData = {
      name,
      orgId,
      orgName,
      sportTypes,
      description,
      creatorId: this.data.userId,
      logo: logo || '',
      members: [
        {
          id: this.data.userId,
          name: '我',
          avatar: '',
          joinedAt: new Date().toISOString(),
          status: 'approved'
        }
      ],
      announcements: [],
      activities: []
    };

    // 显示加载提示
    wx.showLoading({
      title: '创建中...',
      mask: true
    });

    try {
      // 创建社团
      console.log('准备创建社团，数据:', JSON.stringify(clubData));
      const clubId = createClub(clubData);
      console.log('创建社团结果，clubId:', clubId);

      if (clubId) {
        wx.hideLoading();
        wx.showToast({
          title: '创建成功',
          icon: 'success',
          duration: 2000
        });

        // 跳转到社团详情页
        setTimeout(() => {
          console.log('准备跳转到社团详情页，clubId:', clubId);
          wx.redirectTo({
            url: `/pages/club-detail/club-detail?id=${clubId}`,
            success: () => {
              console.log('跳转到社团详情页成功');
            },
            fail: (err) => {
              console.error('跳转到社团详情页失败:', err);
              // 如果跳转失败，返回上一页
              wx.navigateBack();
            }
          });
        }, 2000);
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '创建失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('创建社团过程中发生错误:', error);
      wx.hideLoading();
      wx.showToast({
        title: '创建过程中出错',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 在实际项目中，这里应该上传图片到服务器，获取URL
        // 这里为了演示，直接使用本地临时路径
        this.setData({
          'formData.logo': tempFilePath
        });

        this.validateForm();
      }
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
})