/* pages/order-detail/order-detail.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.order-detail-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: #333;
  box-sizing: content-box;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  width: 90rpx;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 90rpx;
}

/* 内容区域 */
.order-content {
  position: fixed;
  top: calc(90rpx + env(safe-area-inset-top));
  left: 0;
  right: 0;
  bottom: 100rpx;
  background-color: #f5f5f5;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 订单状态样式 */
.order-status {
  padding: 30rpx;
  background-color: #E74C3C;
  color: white;
  display: flex;
  align-items: center;
}

.status-icon {
  margin-right: 20rpx;
}

.icon-circle {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
}

/* 场馆信息卡片样式 */
.venue-info-card {
  margin: 0;
  padding: 30rpx;
  background-color: white;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.venue-image {
  width: 120rpx;
  height: 90rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.venue-details {
  flex: 1;
}

.venue-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.venue-hall {
  font-size: 24rpx;
  color: #666;
}

/* 价格信息样式 */
.price-info {
  margin: 0;
  padding: 0 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.price-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx dashed #eee;
}

.price-item:last-child {
  border-bottom: none;
}

.price-label {
  color: #333;
}

.price-value {
  color: #333;
  font-weight: bold;
}

.discount {
  color: #E74C3C;
}

.total {
  border-top: none;
  padding-top: 20rpx;
  margin-top: 0;
  border-bottom: none;
}

.total .price-label, .total .price-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 订场场次样式 */
.booking-sessions {
  margin: 20rpx 0 0 0;
  padding: 0;
  background-color: white;
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.booking-explanation {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.explanation-icon {
  margin-right: 6rpx;
  color: #E74C3C;
  font-size: 28rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.session-date {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.session-list {
  padding: 0 30rpx;
}

.session-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.session-item:last-child {
  border-bottom: none;
}

.session-venue {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
}

.session-status {
  color: #999;
  font-size: 24rpx;
}

.session-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

/* 参与者样式 */
.participants {
  display: flex;
  margin-bottom: 20rpx;
}

.participant-avatar, .participant-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
}

.participant-avatar image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #E74C3C;
}

.participant-name {
  font-size: 22rpx;
  color: #333;
  margin-top: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 1rpx dashed #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 30rpx;
}

/* 邀请按钮样式 */
.invite-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0;
  padding: 0;
}

/* 收起按钮样式 */
.collapse-button {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 订单信息样式 */
.order-info {
  margin: 20rpx 0 0 0;
  padding: 20rpx 30rpx;
  background-color: white;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  width: 140rpx;
}

.info-value {
  color: #333;
  flex: 1;
  text-align: right;
}

/* 底部提示样式 */
.bottom-tip {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin: 30rpx 0;
}

/* 底部空白区域 */
.bottom-space {
  height: 120rpx;
}

/* 底部操作栏样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.more-button-container {
  position: relative;
}

.more-button {
  font-size: 28rpx;
  color: #666;
}

/* 气泡菜单样式 */
.more-options-bubble {
  position: absolute;
  bottom: 90rpx;
  left: -20rpx;
  width: 200rpx;
  background-color: white;
  border-radius: 10rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  z-index: 101;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.2s ease-out;
}

.more-options-bubble.show {
  opacity: 1;
  transform: translateY(0);
}

.bubble-arrow {
  position: absolute;
  bottom: -16rpx;
  left: 30rpx;
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-top: 16rpx solid white;
}

.bubble-option {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.bubble-option:last-child {
  border-bottom: none;
}

.scan-button {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  text-align: center;
}

/* 签到弹窗样式 */
.sign-in-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sign-in-content {
  width: 80%;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.sign-in-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.sign-in-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.sign-in-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.sign-in-body {
  padding: 30rpx;
}

.sign-in-venue {
  margin-bottom: 30rpx;
}

.sign-in-venue-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.sign-in-venue-hall {
  font-size: 28rpx;
  color: #666;
}

.sign-in-time {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.sign-in-date {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.sign-in-session {
  font-size: 32rpx;
  font-weight: bold;
  color: #E74C3C;
}

.sign-in-status {
  margin-bottom: 40rpx;
  text-align: center;
}

.sign-in-status-text {
  font-size: 28rpx;
  color: #666;
}

.sign-in-button-container {
  margin-top: 20rpx;
}

.sign-in-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin: 0;
  padding: 0;
}

.sign-in-button.disabled {
  background-color: #ccc;
  color: #fff;
}
