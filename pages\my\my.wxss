/* pages/my/my.wxss */
page {
  background-color: white;
  height: 100vh;
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: white;
  justify-content: flex-start; /* 改为从顶部开始排列，而不是space-between */
  align-items: stretch; /* 让子元素填充整个宽度 */
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 0;
  z-index: 100;
  color: white;
  box-sizing: border-box;
  width: 100%;
}

.nav-back {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-top: 3rpx solid white;
  border-left: 3rpx solid white;
  transform: rotate(-45deg);
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

/* 用户信息区域样式 */
.user-info-section {
  margin: 0;
  padding-top: env(safe-area-inset-top);
  background-color: #E74C3C;
  display: flex;
  flex-direction: column;
  padding-bottom: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0 10rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  margin-bottom: 16rpx;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-name {
  font-size: 34rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-align: center;
}

.user-phone {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  text-align: center;
}

/* 用户数据统计 */
.user-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  padding: 10rpx 0;
  margin-top: 5rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  line-height: 1.2;
}

.stat-label {
  font-size: 26rpx;
  color: white;
  margin-top: 6rpx;
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 功能区域 */
.function-section {
  width: 100%;
  margin-top: 0;
  padding-top: 0;
  background-color: white;
  flex: 1; /* 让功能区域占据剩余空间 */
}

/* 分区标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 0 30rpx 10rpx;
  position: relative;
  padding-left: 20rpx;
  padding-top: 15rpx;
  padding-bottom: 5rpx; /* 添加底部间距 */
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #E74C3C;
  border-radius: 4rpx;
}


/* 主要功能入口 */
.function-menu {
  margin: 0;
  background-color: white;
  border-radius: 0;
  overflow: hidden;
  width: 100%;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background-color: rgba(231, 76, 60, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.function-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.function-text {
  font-size: 32rpx;
  color: #333;
  flex: 1;
  font-weight: 500;
}

.function-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 3rpx solid #ccc;
  border-right: 3rpx solid #ccc;
  transform: rotate(45deg);
  margin-right: 10rpx;
}

/* 图标样式 */
.order-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23E74C3C"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.org-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23E74C3C"><path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}

.club-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23E74C3C"><path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/></svg>');
  background-size: 40rpx 40rpx;
  background-position: center;
  background-repeat: no-repeat;
}
