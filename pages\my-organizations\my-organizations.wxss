/* pages/my-organizations/my-organizations.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.my-orgs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
  margin-top: 20rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 60rpx;
  background-color: white;
  border-radius: 16rpx;
  margin: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 90rpx;
  margin-bottom: 30rpx;
  color: #E74C3C;
  opacity: 0.8;
}

.empty-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

/* 单位列表 */
.orgs-list {
  height: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.org-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.org-info {
  display: flex;
  margin-bottom: 24rpx;
  position: relative;
}

.org-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.org-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.org-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.org-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  margin-right: 12rpx;
  line-height: 1.3;
}

.cert-badge {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  background-color: #E74C3C;
  color: white;
  font-weight: 500;
  display: inline-block;
  margin-top: 4rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.org-type {
  font-size: 26rpx;
  color: #555;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.org-address {
  font-size: 26rpx;
  color: #666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 按钮区域 */
.org-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.cancel-button {
  width: 180rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
  margin: 0;
  padding: 0;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #E74C3C;
  border: 1rpx solid #E74C3C;
}

.cancel-button:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
}

/* 底部按钮 */
.bottom-button-container {
  padding: 30rpx 0 60rpx;
  display: flex;
  justify-content: center;
}

.apply-button {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  background-color: #E74C3C;
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.3);
  border: none;
  transition: all 0.3s;
}

.apply-button:active {
  background-color: #c0392b;
  transform: scale(0.98);
}
