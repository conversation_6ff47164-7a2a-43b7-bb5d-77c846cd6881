<!--pages/create-club/create-club.wxml-->
<view class="create-club-container" bindtap="onPageTap">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">创建社团</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <form bindsubmit="submitForm">
      <!-- 社团头像 -->
      <view class="form-item logo-item">
        <view class="form-label">社团头像</view>
        <view class="logo-upload" bindtap="chooseImage">
          <block wx:if="{{formData.logo}}">
            <image
              class="club-logo-preview"
              src="{{formData.logo}}"
              mode="aspectFill"
            ></image>
          </block>
          <block wx:else>
            <view class="logo-placeholder">+</view>
          </block>
          <view class="upload-icon">+</view>
        </view>
      </view>

      <!-- 社团名称 -->
      <view class="form-item">
        <view class="form-label">社团名称</view>
        <input
          class="form-input"
          name="name"
          placeholder="请输入社团名称"
          value="{{formData.name}}"
          bindinput="onNameInput"
        />
      </view>

      <!-- 所属单位 -->
      <view class="form-item">
        <view class="form-label">所属单位</view>
        <picker
          mode="selector"
          range="{{organizations}}"
          range-key="name"
          value="{{orgIndex}}"
          bindchange="onOrgChange"
        >
          <view class="form-picker {{formData.orgId ? '' : 'placeholder'}}">
            {{formData.orgId ? formData.orgName : '请选择所属单位'}}
            <view class="picker-arrow">▼</view>
          </view>
        </picker>
      </view>

      <!-- 运动类型 -->
      <view class="form-item">
        <view class="form-label">运动类型</view>

        <!-- 运动类型选择框 -->
        <view class="form-input sport-type-selector" catchtap="showSportTypeDropdown" data-area="dropdown">
          <text wx:if="{{formData.sportTypes.length > 0}}">已选择 {{formData.sportTypes.length}} 种运动</text>
          <text wx:else>请选择运动类型</text>
          <view class="dropdown-icon {{showSportTypeDropdown ? 'active' : ''}}">▼</view>
        </view>

        <!-- 已选择的运动类型标签 -->
        <view class="selected-sports-container" wx:if="{{formData.sportTypes.length > 0}}" data-area="dropdown">
          <view class="selected-sport-tag" wx:for="{{formData.sportTypes}}" wx:key="*this" data-area="dropdown">
            {{item}}
            <view class="remove-tag" catchtap="removeSportType" data-sport="{{item}}" data-area="dropdown">×</view>
          </view>
        </view>
      </view>

      <!-- 社团介绍 -->
      <view class="form-item">
        <view class="form-label">社团介绍</view>
        <textarea
          class="form-textarea"
          name="description"
          placeholder="请输入社团介绍"
          value="{{formData.description}}"
          bindinput="onDescriptionInput"
          maxlength="200"
        ></textarea>
        <view class="textarea-counter">{{formData.description.length}}/200</view>
      </view>

      <!-- 提交按钮 -->
      <view class="form-button-container">
        <button
          class="submit-button {{formValid ? '' : 'disabled'}}"
          bindtap="onSubmitButtonTap"
          disabled="{{!formValid}}"
          hover-class="button-hover"
        >创建社团</button>
      </view>
    </form>
  </view>

  <!-- 运动类型选择弹窗 -->
  <view class="sport-type-popup {{showSportTypeDropdown ? 'show' : ''}}" catchtap="stopPropagation" data-area="dropdown">
    <view class="popup-content" catchtap="stopPropagation" data-area="dropdown">
      <view class="popup-header" data-area="dropdown">
        <view class="popup-title" data-area="dropdown">选择运动类型</view>
        <view class="popup-close" catchtap="closeDropdown" data-area="dropdown">完成</view>
      </view>

      <view class="popup-search" data-area="dropdown">
        <input
          class="search-input"
          placeholder="搜索运动类型"
          bindinput="onSportSearchInput"
          value="{{sportSearchText}}"
          catchtap="stopPropagation"
          data-area="dropdown"
        />
      </view>

      <view class="popup-body" data-area="dropdown">
        <view
          class="sport-type-item {{item.selected ? 'selected' : ''}}"
          wx:for="{{filteredSportTypes}}"
          wx:key="name"
          catchtap="toggleSportType"
          data-index="{{item.originalIndex}}"
          data-area="dropdown"
        >
          {{item.name}}
        </view>
      </view>
    </view>
  </view>
</view>