<!-- pages/map-poster/map-poster.wxml -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="nav-bar">
    <view class="nav-title-container">
      <image src="/images/union-logo.png" mode="heightFix" class="nav-logo"></image>
      <view class="nav-title">大桥镇15分钟职工文化服务圈</view>
    </view>
  </view>

  <!-- 地图画布 -->
  <view class="map-container">
    <!-- 移除了装饰性角落元素 -->

    <!-- 地图标题 -->
    <view class="map-title">
      <view class="map-title-icon"></view>
      <view class="map-title-text">
        <view>大桥镇</view>
        <view>15分钟职工文化服务圈</view>
      </view>
    </view>

    <canvas canvas-id="mapCanvas" bindtap="onCanvasTap" bindtouchstart="onCanvasTap" class="map-canvas" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"></canvas>
    <view class="map-overlay" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text>加载地图中...</text>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="function-entries">
    <view class="function-entry" bindtap="navigateToOrganizations">
      <view class="function-icon auth-icon"></view>
      <text class="function-label">认证</text>
    </view>
    <view class="function-entry" bindtap="navigateToMyOrders">
      <view class="function-icon order-icon"></view>
      <text class="function-label">订单</text>
    </view>
    <view class="function-entry" bindtap="navigateToClubs">
      <view class="function-icon club-icon"></view>
      <text class="function-label">社团</text>
    </view>
  </view>

  <!-- 推荐场地列表 -->
  <view class="venue-list">
    <view class="venue-list-header">
      <view class="venue-list-title">推荐场地</view>
      <view class="venue-list-filter" wx:if="{{!showAllVenues}}">
        <view class="filter-badge">{{filteredVenues.length}}</view>
        <view class="show-all-btn" bindtap="showAllVenues">显示全部</view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="venues-scroll-view" scroll-into-view="{{scrollToVenueId ? 'venue-' + scrollToVenueId : ''}}" enable-back-to-top="true" enhanced="true" show-scrollbar="false">
      <view wx:if="{{showAllVenues}}" class="venue-list-content">
        <!-- 显示所有场馆 -->
        <view
          id="venue-{{item.id}}"
          class="venue-card {{selectedVenueIds.indexOf(item.id) !== -1 ? 'venue-card-selected' : ''}}"
          wx:for="{{venues}}"
          wx:key="id"
          bindtap="onVenueItemTap"
          data-venue-id="{{item.id}}"
        >
          <image src="{{item.imageUrl || '/images/default-venue.png'}}" mode="aspectFill" class="venue-image"></image>
          <view class="venue-info">
            <view class="venue-name">{{item.name}}</view>
            <view class="venue-type">{{item.type || '体育场馆'}}</view>
            <view class="venue-address">{{item.address}}</view>

            <!-- 运动类型标签 -->
            <view class="venue-sports" wx:if="{{item.sports && item.sports.length > 0}}">
              <view class="sport-tag" wx:for="{{item.sports}}" wx:for-item="sport" wx:key="*this" wx:if="{{index < 3}}">
                {{sport}}
              </view>
              <view class="sport-tag more-tag" wx:if="{{item.sports && item.sports.length > 3}}">
                +{{item.sports.length - 3}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <view wx:else class="venue-list-content">
        <!-- 只显示过滤后的场馆 -->
        <view
          id="venue-{{item.id}}"
          class="venue-card venue-card-selected"
          wx:for="{{filteredVenues}}"
          wx:key="id"
          bindtap="onVenueItemTap"
          data-venue-id="{{item.id}}"
        >
          <image src="{{item.imageUrl || '/images/default-venue.png'}}" mode="aspectFill" class="venue-image"></image>
          <view class="venue-info">
            <view class="venue-name">{{item.name}}</view>
            <view class="venue-type">{{item.type || '体育场馆'}}</view>
            <view class="venue-address">{{item.address}}</view>

            <!-- 运动类型标签 -->
            <view class="venue-sports" wx:if="{{item.sports && item.sports.length > 0}}">
              <view class="sport-tag" wx:for="{{item.sports}}" wx:for-item="sport" wx:key="*this" wx:if="{{index < 3}}">
                {{sport}}
              </view>
              <view class="sport-tag more-tag" wx:if="{{item.sports && item.sports.length > 3}}">
                +{{item.sports.length - 3}}
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 底部导航栏 -->
  <view class="tab-bar">
    <view class="tab-item active">
      <view class="tab-icon poster-icon"></view>
      <text class="tab-text">首页</text>
    </view>
    <view class="tab-item" bindtap="navigateToIndex">
      <view class="tab-icon map-icon"></view>
      <text class="tab-text">地图</text>
    </view>
    <view class="tab-item" bindtap="navigateToMy">
      <view class="tab-icon my-icon"></view>
      <text class="tab-text">我的</text>
    </view>
  </view>
</view>
