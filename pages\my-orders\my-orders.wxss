/* pages/my-orders/my-orders.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.my-orders-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 订单类型选项卡 */
.order-tabs {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.tab-header {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  position: relative;
}

.tab-item.active {
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
  font-weight: bold;
}

/* 加载中提示 */
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表 */
.order-list {
  flex: 1;
  padding: 0 20rpx;
}

/* 无订单提示 */
.no-orders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-orders-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #ccc;
}

.no-orders-text {
  font-size: 28rpx;
  color: #999;
}

/* 订单列表项 */
.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-number {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
  color: #E74C3C;
}

.order-status.pending {
  color: #f39c12; /* 橙色 */
}

.order-status.processing {
  color: #E74C3C; /* 红色 */
}

.order-status.completed {
  color: #27ae60; /* 绿色 */
}

.order-status.refunded {
  color: #7f8c8d; /* 灰色 */
}

.venue-info {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.venue-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.venue-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.venue-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.venue-hall {
  font-size: 26rpx;
  color: #666;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-price {
  font-size: 26rpx;
  color: #666;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.order-actions {
  display: flex;
}

.action-button {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
}

.action-button.more {
  color: #666;
  border: 1rpx solid #ddd;
}

.action-button.scan {
  color: #fff;
  background-color: #E74C3C;
}
