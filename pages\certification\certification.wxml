<!--pages/certification/certification.wxml-->
<view class="certification-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">个人单位认证</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 认证成功页面 -->
  <block wx:if="{{certificationSuccess}}">
    <scroll-view scroll-y="true" class="page-scroll">
      <view class="success-container">
        <view class="success-icon">✓</view>
        <view class="success-title">认证申请已提交</view>
        <view class="success-message">您的个人认证申请已成功提交，我们将尽快审核</view>
        <view class="success-org">申请认证单位：{{orgName}}</view>
        <view class="success-type">单位类型：{{orgType === 'enterprise' ? '企业单位' : '事业单位'}}</view>
        <view class="success-note">认证成功后，您将获得该单位的专属权益</view>
        <button class="back-button" bindtap="backToHome">返回首页</button>
      </view>
    </scroll-view>
  </block>

  <!-- 认证申请表单 -->
  <block wx:else>
    <scroll-view scroll-y="true" class="page-scroll">
      <view class="form-container">
      <!-- 个人认证信息 -->
      <view class="form-section">
        <view class="section-title">个人认证申请</view>
        <view class="section-desc">您正在申请个人与企事业单位的关联认证</view>
        <view class="org-info">
          <view class="info-item">
            <view class="info-label">申请单位</view>
            <view class="info-value">{{orgName}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">单位类型</view>
            <view class="info-value">{{orgType === 'enterprise' ? '企业单位' : '事业单位'}}</view>
          </view>
        </view>
      </view>

      <!-- 上传凭证 -->
      <view class="form-section">
        <view class="section-title">上传凭证</view>
        <view class="section-desc">请上传您与该单位的关联证明材料，例如：</view>
        <view class="cert-examples">
          <view class="example-item">• 工作证、员工证、单位工牌</view>
          <view class="example-item">• 劳动合同、聘用证明</view>
          <view class="example-item">• 单位开具的在职证明</view>
          <view class="example-item">• 单位盖章的认证申请表</view>
        </view>

        <!-- 已上传图片列表 -->
        <view class="upload-list">
          <view class="upload-item" wx:for="{{uploadedFiles}}" wx:key="path">
            <image class="upload-image" src="{{item.path}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-icon" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>

          <!-- 上传按钮 -->
          <view class="upload-button" bindtap="uploadCertificate" wx:if="{{uploadedFiles.length < maxUploadCount}}">
            <view class="upload-icon">+</view>
            <view class="upload-text">上传凭证</view>
          </view>
        </view>

        <view class="upload-tip">最多上传{{maxUploadCount}}张图片</view>
      </view>

      <!-- 认证说明 -->
      <view class="form-section">
        <view class="section-title">认证说明</view>
        <view class="cert-rules">
          <view class="rule-item">1. 请确保上传的证明材料真实有效，我们将严格保护您的隐私</view>
          <view class="rule-item">2. 个人认证申请提交后，我们将在1-3个工作日内完成审核</view>
          <view class="rule-item">3. 认证通过后，您将获得该单位专属权益，包括场馆预订优惠等</view>
          <view class="rule-item">4. 每个微信账号仅能认证一个企事业单位身份</view>
          <view class="rule-item">5. 如有疑问，请联系客服：400-123-4567</view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-button {{submitDisabled ? 'disabled' : ''}}" bindtap="submitCertification" disabled="{{submitDisabled}}">提交认证申请</button>
      </view>
    </view>
    </scroll-view>
  </block>
</view>
