<!--pages/organizations/organizations.wxml-->
<view class="organizations-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">企事业单位</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 搜索框 -->
  <view class="search-box">
    <view class="search-input-container full-width">
      <icon type="search" size="14" color="#999"></icon>
      <input
        class="search-input"
        placeholder="搜索企事业单位"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="doSearch"
        confirm-type="search"
      />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 单位类型选项卡 -->
    <view class="org-tabs">
      <view class="tab-header">
        <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
        <view class="tab-item {{currentTab === 'enterprise' ? 'active' : ''}}" bindtap="switchTab" data-tab="enterprise">企业单位</view>
        <view class="tab-item {{currentTab === 'institution' ? 'active' : ''}}" bindtap="switchTab" data-tab="institution">事业单位</view>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 单位列表 -->
    <scroll-view class="org-list" scroll-y="true" wx:if="{{!loading}}">
      <!-- 无单位提示 -->
      <view class="no-orgs" wx:if="{{filteredOrganizations.length === 0}}">
        <view class="no-orgs-icon">🏢</view>
        <view class="no-orgs-text">未找到符合条件的单位</view>
      </view>

      <!-- 单位列表项 -->
      <view class="org-item" wx:for="{{filteredOrganizations}}" wx:key="id">
        <!-- 单位信息 -->
        <view class="org-info">
          <image class="org-logo" src="{{item.logo}}" mode="aspectFill"></image>
          <view class="org-details">
            <view class="org-name-container">
              <view class="org-name">{{item.name}}</view>
              <view class="cert-badge" wx:if="{{item.certified}}">已认证</view>
            </view>
            <view class="org-type">{{item.type === 'enterprise' ? '企业单位' : '事业单位'}}</view>
            <view class="org-address">{{item.address}}</view>
          </view>
        </view>

        <!-- 单位描述 -->
        <view class="org-description">{{item.description}}</view>

        <!-- 操作按钮 -->
        <view class="org-actions">
          <button class="apply-button {{item.certified ? 'cancel-button' : ''}}" bindtap="applyForCertification" data-id="{{item.id}}">
            {{item.certified ? '取消认证' : '申请个人认证'}}
          </button>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
