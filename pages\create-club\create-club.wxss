/* pages/create-club/create-club.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.create-club-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 表单区域 */
.form-container {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.logo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.logo-upload {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 20rpx;
  border: 1rpx dashed #ddd;
  overflow: hidden;
}

.club-logo-preview {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #999;
  background-color: #f0f0f0;
  border-radius: 16rpx;
}

.upload-icon {
  position: absolute;
  font-size: 60rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 16rpx;
}

.logo-upload:hover .upload-icon,
.logo-upload:active .upload-icon {
  opacity: 1;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.form-input {
  height: 80rpx;
  background-color: white;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #eee;
}

.form-picker {
  height: 80rpx;
  background-color: white;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-picker.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.sport-type-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  height: 80rpx;
  position: relative;
}

.dropdown-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s;
}

.dropdown-icon.active {
  transform: rotate(180deg);
}

.selected-sports-container {
  display: flex;
  flex-wrap: wrap;
  padding: 15rpx 0;
  margin-top: 10rpx;
}

.selected-sport-tag {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.remove-tag {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 弹窗样式 */
.sport-type-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: none;
}

.sport-type-popup.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 90%;
  max-height: 80vh;
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 30rpx;
  color: #E74C3C;
  font-weight: bold;
}

.popup-search {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  border: none;
}

.popup-body {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  max-height: 60vh;
  overflow-y: auto;
}

.sport-type-item {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  color: #666;
}

.sport-type-item.selected {
  background-color: #E74C3C;
  color: white;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: white;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.form-button-container {
  margin-top: 60rpx;
  padding-bottom: 60rpx;
}

.submit-button {
  background-color: #E74C3C;
  color: white;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.submit-button.disabled {
  background-color: #ccc;
  color: #fff;
}

.button-hover {
  background-color: #c0392b !important;
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}