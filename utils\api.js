// utils/api.js
// 导入mock数据
import * as mockData from '../mock/venues';
import { getOrderById } from './storage';

// 模拟API延迟
const simulateDelay = (ms = 300) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 获取所有场地
const getVenues = async () => {
  // 模拟网络延迟
  await simulateDelay();

  // 返回mock数据
  return {
    code: 0,
    message: 'success',
    data: mockData.venues
  };
};

// 搜索场地
const searchVenues = async (keyword) => {
  // 模拟网络延迟
  await simulateDelay();

  // 使用mock数据的搜索函数
  const results = mockData.searchVenues(keyword);

  return {
    code: 0,
    message: 'success',
    data: results
  };
};

// 根据ID获取场地详情
const getVenueById = async (id) => {
  // 模拟网络延迟
  await simulateDelay();

  // 查找对应ID的场地
  const venue = mockData.venues.find(v => v.id === id);

  if (!venue) {
    return {
      code: 404,
      message: '未找到该场地',
      data: null
    };
  }

  return {
    code: 0,
    message: 'success',
    data: venue
  };
};

// 获取场地预订信息
const getVenueBookingInfo = async (venueId, sportType, date) => {
  // 模拟网络延迟
  await simulateDelay();

  // 查找对应ID的场地
  const venue = mockData.venues.find(v => v.id === venueId);

  if (!venue) {
    return {
      code: 404,
      message: '未找到该场地',
      data: null
    };
  }

  // 这里可以根据sportType和date筛选相关的预订信息
  // 目前使用模拟数据

  return {
    code: 0,
    message: 'success',
    data: {
      venue: venue,
      bookingInfo: {
        // 模拟预订信息
        date: date || new Date().toISOString().split('T')[0],
        sportType: sportType || '',
        // 其他预订相关信息可以在这里添加
      }
    }
  };
};

// 提交预订
const submitBooking = async (bookingData) => {
  // 模拟网络延迟
  await simulateDelay(1000);

  // 模拟预订成功
  return {
    code: 0,
    message: 'success',
    data: {
      orderId: 'ORDER_' + Date.now(),
      ...bookingData
    }
  };
};

// 获取订单详情
const getOrderDetail = async (orderId) => {
  // 模拟网络延迟
  await simulateDelay(500);

  // 先从本地存储中获取订单数据
  const localOrder = getOrderById(orderId);

  // 如果本地存储中有订单数据，直接返回
  if (localOrder) {
    return {
      code: 0,
      message: 'success',
      data: localOrder
    };
  }

  // 如果本地存储中没有订单数据，则返回模拟数据
  // 模拟订单数据
  const mockOrder = {
    id: orderId,
    status: 'processing', // processing, completed, cancelled
    statusText: '订单进行中',

    // 场馆信息
    venue: {
      id: 1,
      name: '万众运动（大桥镇浙江清华长三角研究院店）',
      imageUrl: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
      hall: 'A座2层羽毛球馆-A座2楼羽毛球馆-2号场'
    },

    // 预订信息
    booking: {
      date: '2025.05.13',
      day: '周二',
      sessions: [
        {
          time: '11:00-12:00',
          court: '2号场',
          price: 60
        },
        {
          time: '12:00-13:00',
          court: '2号场',
          price: 60
        }
      ],
      totalSessions: 2
    },

    // 价格信息
    price: {
      total: 120,
      discount: 120,
      final: 0
    },

    // 订单信息
    orderInfo: {
      orderNumber: '202505122343500000918907',
      user: '天风君',
      phone: '135****0573',
      orderTime: '2025-05-12 23:43:51',
      payTime: '2025-05-12 23:43:51',
      payMethod: '人工收款-微信'
    },

    // 参与者
    participants: [
      {
        id: 1,
        avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        name: '我',
        isMe: true
      }
    ]
  };

  return {
    code: 0,
    message: 'success',
    data: mockOrder
  };
};

export {
  getVenues,
  searchVenues,
  getVenueById,
  getVenueBookingInfo,
  submitBooking,
  getOrderDetail
};
