<!--pages/venue-booking/venue-booking.wxml-->
<view class="booking-container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <block wx:if="{{!loading && venue}}">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" bindtap="navigateBack">
        <view class="back-icon">←</view>
      </view>
      <view class="nav-title">场地预订</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 内容区域（可滚动） -->
    <scroll-view scroll-y="true" class="booking-scroll-view" enhanced="true">
      <!-- 顶部场馆图片 -->
      <view class="venue-image-container">
        <image src="{{venue.imageUrl}}" mode="aspectFill" class="venue-image"></image>
        <view class="venue-image-overlay"></view>
        <button class="image-share-button" open-type="share" plain="true">
          <view class="share-icon"></view>
          <text class="share-text">分享</text>
        </button>
      </view>

      <!-- 认证提示 -->
      <view class="certification-notice" wx:if="{{!hasCertification}}">
        <view class="notice-icon">i</view>
        <view class="notice-text">职工认证后可享订场优惠</view>
        <view class="notice-action" bindtap="navigateToCertification">前往认证</view>
      </view>

      <!-- 日期选择 -->
      <view class="date-selector">
        <view class="date-tabs">
          <view
            wx:for="{{dateOptions}}"
            wx:key="date"
            class="date-tab {{selectedDate === item.date ? 'active' : ''}}"
            bindtap="selectDate"
            data-date="{{item.date}}">
            <view class="date-day">{{item.day}}</view>
            <view class="date-value">{{item.value}}</view>
          </view>
          <view class="more-dates" bindtap="showMoreDates">
            <view class="calendar-icon"></view>
            <view class="more-dates-text">更多日期</view>
          </view>
        </view>
      </view>

      <!-- 场馆选择 -->
      <view class="hall-selector">
        <view
          wx:for="{{venue.bookingHalls}}"
          wx:key="id"
          class="hall-tab {{selectedHallId === item.id ? 'active' : ''}}"
          bindtap="selectHall"
          data-id="{{item.id}}">
          {{item.name}}
          <view class="active-hall-indicator"></view>
        </view>
        <view class="hall-indicator-line"></view>
      </view>

      <!-- 时间段选择 -->
      <view class="time-slots-container">
        <!-- 场地列标题 -->
        <view class="court-headers">
          <view
            wx:for="{{selectedHall.courts}}"
            wx:key="id"
            class="court-header">
            {{item.name}}
          </view>
        </view>

        <view class="time-slots-grid">
          <!-- 无可用场次提示 -->
          <block wx:if="{{timeSlots.length === 0}}">
            <view class="no-slots-message">
              <view class="no-slots-text">{{noSlotsMessage}}</view>
            </view>
          </block>

          <!-- 时间段和场地格子 -->
          <block wx:else>
            <view
              wx:for="{{timeSlots}}"
              wx:key="time"
              class="time-slot-row">
              <view class="time-label">{{item.time}}</view>
              <view class="court-slots">
                <view
                  wx:for="{{item.courts}}"
                  wx:for-item="court"
                  wx:for-index="courtIndex"
                  wx:key="id"
                  class="time-slot {{court.status}} {{court.selected ? 'selected' : ''}}"
                  bindtap="toggleTimeSlot"
                  data-time="{{item.time}}"
                  data-court-id="{{court.id}}"
                  data-price="{{court.price}}"
                  data-status="{{court.status}}">
                  <block wx:if="{{hasCertification && court.status === 'available'}}">
                    <view class="time-slot-price">
                      <text class="original-slot-price">¥{{court.price}}</text>
                      <text class="discounted-slot-price">¥{{court.discountedPrice}}</text>
                    </view>
                    <view class="time-slot-discount">{{discountName}}</view>
                  </block>
                  <block wx:else>
                    <view class="time-slot-price">¥{{court.price}}</view>
                  </block>
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>

      <!-- 底部空白区域，确保内容不被底部栏遮挡 -->
      <view class="bottom-spacer"></view>
    </scroll-view>

    <!-- 选择状态说明 - 固定在底部 -->
    <view class="fixed-booking-status-legend">
      <view class="status-item">
        <view class="status-icon selected"></view>
        <text>已选</text>
      </view>
      <view class="status-item">
        <view class="status-icon unavailable"></view>
        <text>不可选</text>
      </view>
      <view class="status-item">
        <view class="status-icon available"></view>
        <text>可选</text>
      </view>
    </view>

    <!-- 已选场次展示 - 固定在底部 -->
    <view class="selected-slots-container" wx:if="{{selectedSlots.length > 0}}">
      <scroll-view scroll-x="true" class="selected-slots-scroll" enhanced="true" show-scrollbar="false">
        <view class="selected-slots-list">
          <view
            wx:for="{{selectedSlots}}"
            wx:key="id"
            class="selected-slot-card">
            <view class="selected-slot-info">
              <view class="selected-slot-time">{{item.time}} - {{item.endTime}}</view>
              <view class="selected-slot-court">{{item.courtName}}</view>
              <view class="selected-slot-price-info" wx:if="{{item.hasDiscount}}">
                <text class="selected-slot-original-price">¥{{item.price}}</text>
                <text class="selected-slot-final-price">¥{{item.finalPrice}}</text>
              </view>
            </view>
            <view class="selected-slot-delete" bindtap="removeTimeSlot" data-id="{{item.id}}">×</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="bottom-actions">
        <view class="price-container">
          <view class="price-icon">
            <text class="selected-count">{{selectedSlots.length}}</text>
          </view>
          <view class="price-text">
            <view wx:if="{{hasCertification}}" class="original-price">¥{{originalPrice}}.00</view>
            <view class="total-price-line">
              <text>合计：</text><text class="price">¥{{totalPrice}}.00</text>
            </view>
          </view>
        </view>
        <view class="action-buttons">
          <button
            class="confirm-button {{(timeSlots.length === 0 || selectedSlots.length === 0) ? 'disabled' : ''}}"
            bindtap="confirmBooking"
            disabled="{{timeSlots.length === 0 || selectedSlots.length === 0}}">
            选好了
          </button>
        </view>
      </view>
    </view>
  </block>

  <!-- 场馆规则弹窗 -->
  <view class="modal venue-rules-modal" wx:if="{{showRulesModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">订场说明</view>
        <view class="modal-close" bindtap="closeRulesModal">×</view>
      </view>
      <scroll-view class="modal-body" scroll-y="true" enhanced="true" show-scrollbar="true" bounces="true" enable-back-to-top="true">
        <view class="rules-wrapper">
          <view class="rules-content">
            <view class="rule-item">1、此球场为无烟球场，场内任何区域严禁吸烟。</view>
            <view class="rule-item">2、请自觉维护好球场内卫生，请勿随地吐痰，口香糖、饮料瓶等杂物请自觉放入垃圾桶。</view>
            <view class="rule-item">3、请自觉保管好自己随身携带的物品，遗失自负。</view>
            <view class="rule-item">4、入场人员必须遵守场地的管理规定，保护好场地内的智能设备，不得破坏场地的电源电路、照明等各类设施，防止意外事故发生。</view>
            <view class="rule-item">5、患有心脏病、高血压、以及一切不能从事剧烈运动疾病的客人谨慎入内，如因此在运动中发生意外情况，本场地有义务组织救援，但不承担任何法律和经济赔偿责任。</view>
            <view class="rule-item rule-highlight">6、禁止穿钉鞋、黑胶底、高跟鞋、皮鞋等其他破坏场地的鞋类进场运动投篮，其余运动鞋正常进场。</view>
          </view>
          <view class="refund-rules">
            <view class="refund-title">退款说明</view>
            <view class="rule-item">1、下单支付后10分钟内退订可全额退款。</view>
            <view class="rule-item">2、超过10分钟之后的退款，按以下规则执行：</view>
            <view class="rule-item">距离开场前480分钟以上退订，按订单实付金额退回100.00%</view>
            <view class="rule-item">距离开场前240分钟以上480分钟以下退订，按订单实付金额退回80.00%</view>
            <view class="rule-item">距离开场前120分钟以上240分钟以下退订，按订单实付金额退回50.00%</view>
            <view class="rule-item">距离开场前120分钟以内退订，按订单实付金额退回0.00%</view>
          </view>
        </view>
      </scroll-view>
      <view class="modal-footer">
        <button class="agree-button" bindtap="agreeRules">我已阅读并同意</button>
      </view>
    </view>
  </view>

  <!-- 确认订单弹窗 -->
  <view class="modal order-confirm-modal" wx:if="{{showOrderModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">确认订单</view>
        <view class="modal-close" bindtap="closeOrderModal">×</view>
      </view>
      <view class="modal-body">
        <view class="order-venue-info">
          <image src="{{venue.imageUrl}}" mode="aspectFill" class="order-venue-image"></image>
          <view class="order-venue-details">
            <view class="order-venue-name">{{venue.name}}</view>
            <view class="order-venue-hall">{{selectedHall.name}}</view>
          </view>
        </view>

        <view class="order-date">{{selectedDate}} {{weekDayText}}</view>

        <view class="order-slots">
          <view
            wx:for="{{selectedSlots}}"
            wx:key="id"
            class="order-slot-item">
            <view wx:if="{{item.hasDiscount}}" class="order-slot-discount-tag">{{discountName}}</view>
            <view class="order-slot-time">{{item.time}}</view>
            <view class="order-slot-court">{{item.courtName}}</view>
            <view class="order-slot-price">
              <block wx:if="{{item.hasDiscount}}">
                <text class="order-original-price">¥{{item.price}}</text>
                <text class="order-final-price">¥{{item.finalPrice}}</text>
              </block>
              <block wx:else>
                <text>¥{{item.price}}</text>
              </block>
            </view>
          </view>
        </view>

        <view class="order-price-info">
          <view class="order-price-row">
            <view class="order-price-label">商品总价</view>
            <view class="order-price-value">¥ {{totalPrice}}.00</view>
          </view>
          <view class="order-price-row">
            <view class="order-price-label">优惠券</view>
            <view class="order-price-value coupon-value">暂无可用</view>
          </view>
          <view class="order-price-row total-row">
            <view class="order-price-label">应付金额</view>
            <view class="order-price-value">¥ {{totalPrice}}.00</view>
          </view>
        </view>

        <view class="order-rules-summary">
          <view class="order-rules-title">订场说明</view>
          <view class="order-rules-content">
            <text class="rule-item">1、此球场为无烟球场，场内任何区域严禁吸烟。</text>
            <text class="rule-item">2、请自觉维护好球场内卫生，请勿随地吐痰，口香糖、饮料瓶等杂物请自觉放入垃圾桶。</text>
            <text class="rule-item">3、请自觉保管好自己随身携带的物品，遗失自负。</text>
            <text class="rule-item">4、入场人员必须遵守场地的管理规定，保护好场地内的智能设备，不得破坏场地的电源电路、照明等各类设施，防止意外事故发生。</text>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="pay-button" bindtap="processPayment">
          确认支付 ¥ {{totalPrice}}.00
        </button>
      </view>
    </view>
  </view>
</view>
