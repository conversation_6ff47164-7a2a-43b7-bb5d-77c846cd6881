/* pages/my-clubs/my-clubs.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.my-clubs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
}

/* 社团类型选项卡 */
.club-tabs {
  background-color: white;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
}

.tab-header {
  display: flex;
  height: 90rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: #E74C3C;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #E74C3C;
  border-radius: 3rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态提示 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 60rpx;
  background-color: white;
  border-radius: 16rpx;
  margin: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 90rpx;
  margin-bottom: 30rpx;
  color: #E74C3C;
  opacity: 0.8;
}

.empty-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 20rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.browse-button, .create-button {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  background-color: #E74C3C;
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.3);
  border: none;
  transition: all 0.3s;
}

.browse-button:active, .create-button:active {
  background-color: #c0392b;
  transform: scale(0.98);
}

/* 社团列表 */
.clubs-list {
  height: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.club-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}

.club-info {
  display: flex;
  margin-bottom: 24rpx;
  position: relative;
}

.club-logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
}

.club-logo {
  width: 130rpx;
  height: 130rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.club-logo-placeholder {
  width: 130rpx;
  height: 130rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.club-member-count {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  width: 130rpx;
  background-color: #f8f8f8;
  padding: 4rpx 0;
  border-radius: 10rpx;
}

.club-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.club-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.club-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  margin-right: 12rpx;
  line-height: 1.3;
}

.club-creator-badge, .club-member-badge, .club-pending-badge {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: white;
  font-weight: 500;
  display: inline-block;
  margin-top: 4rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.club-creator-badge {
  background-color: #E74C3C;
}

.club-member-badge {
  background-color: #2ecc71;
}

.club-pending-badge {
  background-color: #f39c12;
}

.club-org {
  font-size: 26rpx;
  color: #555;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.club-sports {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.sport-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  display: inline-block;
}

/* 按钮区域 */
.club-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.enter-button, .cancel-button {
  width: 180rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
  margin: 0;
  padding: 0;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: none;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.enter-button {
  background-color: white;
  color: #333;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.enter-button:active {
  background-color: #f5f5f5;
  transform: scale(0.98);
}

.cancel-button {
  background-color: #f39c12;
  color: white;
}

.cancel-button:active {
  background-color: #d35400;
  transform: scale(0.98);
}

/* 创建社团按钮 */
.create-club-button {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 110rpx;
  height: 110rpx;
  background-color: #E74C3C;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  box-shadow: 0 6rpx 24rpx rgba(231, 76, 60, 0.4);
  z-index: 10;
  transition: all 0.3s;
}

.create-club-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}
