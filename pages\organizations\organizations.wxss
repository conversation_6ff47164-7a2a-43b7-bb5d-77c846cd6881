/* pages/organizations/organizations.wxss */
page {
  background-color: #f5f5f5;
  height: 100%;
}

.organizations-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  height: 70rpx;
  margin-right: 20rpx;
}

.search-input-container.full-width {
  margin-right: 0;
  width: 100%;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.search-clear {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
}

.search-button {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #E74C3C;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 单位类型选项卡 */
.org-tabs {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.tab-header {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  position: relative;
}

.tab-item.active {
  color: #E74C3C;
  background-color: rgba(231, 76, 60, 0.1);
  font-weight: bold;
}

/* 加载中提示 */
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 单位列表 */
.org-list {
  flex: 1;
  padding: 0 20rpx;
}

/* 无单位提示 */
.no-orgs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-orgs-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  color: #ccc;
}

.no-orgs-text {
  font-size: 28rpx;
  color: #999;
}

/* 单位列表项 */
.org-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.org-info {
  display: flex;
  margin-bottom: 20rpx;
}

.org-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.org-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.org-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.org-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.cert-badge {
  font-size: 22rpx;
  color: white;
  background-color: #27ae60;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
}

.org-type {
  font-size: 26rpx;
  color: #E74C3C;
  margin-bottom: 10rpx;
}

.org-address {
  font-size: 26rpx;
  color: #666;
}

.org-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.org-actions {
  display: flex;
  justify-content: flex-end;
}

.apply-button {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #E74C3C;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  margin: 0;
  padding: 0;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}
