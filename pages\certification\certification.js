// pages/certification/certification.js
import { saveCertification } from '../../utils/storage';

Page({
  data: {
    orgId: null,
    orgName: '',
    orgType: '',
    uploadedFiles: [],
    maxUploadCount: 3,
    submitDisabled: true,
    certificationSuccess: false
  },

  onLoad: function(options) {
    const { orgId, orgName, orgType } = options;

    if (!orgId || !orgName) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orgId,
      orgName,
      orgType: orgType || 'enterprise'
    });
  },

  // 上传凭证
  uploadCertificate: function() {
    const that = this;
    const { uploadedFiles, maxUploadCount } = this.data;

    if (uploadedFiles.length >= maxUploadCount) {
      wx.showToast({
        title: `最多上传${maxUploadCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: maxUploadCount - uploadedFiles.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        const newFiles = tempFiles.map(file => ({
          path: file.tempFilePath,
          size: file.size,
          name: file.tempFilePath.split('/').pop()
        }));

        const updatedFiles = [...uploadedFiles, ...newFiles];

        that.setData({
          uploadedFiles: updatedFiles,
          submitDisabled: updatedFiles.length === 0
        });
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const { uploadedFiles } = this.data;

    wx.previewImage({
      current: uploadedFiles[index].path,
      urls: uploadedFiles.map(file => file.path)
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const { uploadedFiles } = this.data;

    uploadedFiles.splice(index, 1);

    this.setData({
      uploadedFiles,
      submitDisabled: uploadedFiles.length === 0
    });
  },

  // 提交认证申请
  submitCertification: function() {
    if (this.data.submitDisabled) return;

    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 模拟上传过程
    setTimeout(() => {
      // 创建认证记录
      const certificationData = {
        id: 'CERT_' + Date.now(),
        orgId: this.data.orgId,
        orgName: this.data.orgName,
        orgType: this.data.orgType,
        status: 'approved', // 默认自动通过
        statusText: '已认证',
        applyTime: new Date().toISOString(),
        approveTime: new Date().toISOString(),
        files: this.data.uploadedFiles.map(file => file.path)
      };

      // 保存认证记录
      const saveResult = saveCertification(certificationData);

      wx.hideLoading();

      if (saveResult) {
        // 认证成功
        this.setData({
          certificationSuccess: true
        });
      } else {
        wx.showToast({
          title: '保存认证记录失败',
          icon: 'none'
        });
      }
    }, 1500);
  },

  // 返回首页
  backToHome: function() {
    // 使用reLaunch代替switchTab，因为首页不是tabBar页面
    wx.reLaunch({
      url: '/pages/index/index'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
});
