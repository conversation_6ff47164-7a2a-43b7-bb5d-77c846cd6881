/* pages/club-management/club-management.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  height: 90rpx;
  background-color: #E74C3C; /* 工会红色 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: env(safe-area-inset-top); /* 适配iPhone刘海屏 */
  z-index: 100;
  color: white;
}

.nav-back {
  padding: 0 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
}

.nav-placeholder {
  width: 80rpx;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-text {
  font-size: 30rpx;
  color: #999;
}

/* 表单区域 */
.form-container {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.form-input {
  height: 80rpx;
  background-color: white;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #eee;
}

.sport-types-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.sport-type-item {
  padding: 10rpx 20rpx;
  background-color: white;
  border: 1rpx solid #eee;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  color: #666;
}

.sport-type-item.selected {
  background-color: #E74C3C;
  color: white;
  border-color: #E74C3C;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background-color: white;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.form-button-container {
  margin-top: 60rpx;
  padding-bottom: 60rpx;
}

.submit-button {
  background-color: #E74C3C;
  color: white;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.submit-button.disabled {
  background-color: #ccc;
  color: #fff;
}