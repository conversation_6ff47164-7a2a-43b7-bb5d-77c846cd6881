// pages/venue-list/venue-list.js
import * as api from '../../utils/api';

Page({
  data: {
    venues: [],
    loading: true,
    searchKeyword: '',
    // 筛选相关
    showFilterPanel: false,
    showSortDropdown: false,
    showSportTypeDropdown: false,
    selectedSportTypes: [],
    selectedVenueTypes: [],
    // 排序方式
    sortOptions: [
      { id: 'comprehensive', name: '综合排序' },
      { id: 'distance', name: '距离优先' },
      { id: 'popularity', name: '人气优先' }
    ],
    selectedSortOption: 'comprehensive',
    // 运动类型
    sportTypes: [
      { id: 'all', name: '全部', selected: true },
      { id: 'badminton', name: '羽毛球', selected: false },
      { id: 'basketball', name: '篮球', selected: false },
      { id: 'football', name: '足球', selected: false },
      { id: 'tennis', name: '网球', selected: false },
      { id: 'pingpong', name: '乒乓球', selected: false },
      { id: 'swimming', name: '游泳', selected: false },
      { id: 'fitness', name: '健身', selected: false },
      { id: 'yoga', name: '瑜伽', selected: false },
      { id: 'volleyball', name: '排球', selected: false },
      { id: 'billiards', name: '台球', selected: false },
      { id: 'dance', name: '舞蹈', selected: false }
    ],
    // 场馆类型
    venueTypes: [
      { id: 'all', name: '全部', selected: true },
      { id: 'national', name: '国家社区运动中心', selected: false },
      { id: 'campus', name: '校园场地', selected: false },
      { id: 'public', name: '公共场地', selected: false },
      { id: 'commercial', name: '商业体育', selected: false }
    ],
    showFilterDropdown: false
  },

  onLoad: function(options) {
    // 获取用户位置
    this.getUserLocation();

    // 加载场馆数据
    this.loadVenueData();

    // 如果有搜索关键词，则设置搜索关键词并执行搜索
    if (options.keyword) {
      this.setData({
        searchKeyword: options.keyword,
        showSearchInput: true
      });
      this.searchVenues(options.keyword);
    }
  },

  // 获取用户位置
  getUserLocation: function() {
    const that = this;
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        const latitude = res.latitude;
        const longitude = res.longitude;

        that.setData({
          latitude,
          longitude
        });

        // 更新场馆距离
        if (that.data.venues.length > 0) {
          that.updateVenueDistance(latitude, longitude);
        }
      },
      fail: function() {
        wx.showToast({
          title: '获取位置失败，请检查位置权限',
          icon: 'none'
        });
      }
    });
  },

  // 加载场馆数据
  loadVenueData: function() {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    // 从API获取数据
    api.getVenues()
      .then(res => {
        wx.hideLoading();
        // 使用API返回的数据
        const venuesData = res.data || [];

        // 确保所有必要的字段都存在
        const venues = venuesData.map(venue => {
          // 确保sports字段存在
          if (!venue.sports) {
            venue.sports = [];
          }

          const distanceValue = venue.distance || 0;
          // 格式化距离显示
          const formattedDistance = this.formatDistance(distanceValue);

          // 确保其他必要字段存在
          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: distanceValue,
            formattedDistance: formattedDistance,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        that.setData({
          venues,
          loading: false
        });

        // 更新场馆距离
        if (that.data.latitude && that.data.longitude) {
          that.updateVenueDistance(that.data.latitude, that.data.longitude);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取场馆数据失败', err);
        wx.showToast({
          title: '获取场馆数据失败',
          icon: 'none'
        });
      });
  },

  // 更新场馆距离
  updateVenueDistance: function(userLat, userLng) {
    const venues = this.data.venues.map(venue => {
      // 计算距离（实际项目中应使用更精确的算法）
      const distance = this.calculateDistance(
        userLat, userLng,
        venue.latitude, venue.longitude
      );

      const distanceValue = Math.round(distance);
      // 格式化距离显示
      const formattedDistance = this.formatDistance(distanceValue);

      return {
        ...venue,
        distance: distanceValue,
        formattedDistance: formattedDistance
      };
    });

    // 按距离排序
    if (this.data.selectedSortOption === 'distance') {
      venues.sort((a, b) => a.distance - b.distance);
    }

    this.setData({
      venues
    });
  },

  // 格式化距离显示
  formatDistance: function(distance) {
    if (distance < 1000) {
      return distance + '米';
    } else {
      return (distance / 1000).toFixed(1) + '公里';
    }
  },

  // 计算两点之间的距离（简化版，仅用于演示）
  calculateDistance: function(lat1, lng1, lat2, lng2) {
    const R = 6371000; // 地球半径，单位米
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  },

  // 角度转弧度
  deg2rad: function(deg) {
    return deg * (Math.PI / 180);
  },

  // 导航到地图页面
  navigateToMap: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 切换综合排序下拉菜单
  toggleSortDropdown: function() {
    this.setData({
      showSortDropdown: !this.data.showSortDropdown,
      showSportTypeDropdown: false,
      showFilterPanel: false
    });
  },

  // 切换运动类型下拉菜单
  toggleSportTypeDropdown: function() {
    this.setData({
      showSportTypeDropdown: !this.data.showSportTypeDropdown,
      showSortDropdown: false,
      showFilterPanel: false
    });
  },

  // 搜索关键词变化
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 清除搜索关键词
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    // 重新加载所有场馆数据
    this.loadVenueData();
  },

  // 执行搜索
  searchVenues: function(keyword) {
    const that = this;
    const searchKeyword = keyword || this.data.searchKeyword;

    if (!searchKeyword) {
      this.loadVenueData();
      return;
    }

    wx.showLoading({
      title: '搜索中...',
    });

    // 调用API搜索场馆
    api.searchVenues(searchKeyword)
      .then(res => {
        wx.hideLoading();
        // 使用API返回的数据
        const filteredVenues = res.data || [];

        if (filteredVenues.length === 0) {
          wx.showToast({
            title: '未找到相关场馆',
            icon: 'none'
          });
          // 清空场馆数据
          that.setData({
            venues: [],
            loading: false
          });
          return;
        }

        // 确保所有必要的字段都存在
        const processedVenues = filteredVenues.map(venue => {
          // 确保sports字段存在
          if (!venue.sports) {
            venue.sports = [];
          }

          const distanceValue = venue.distance || 0;
          // 格式化距离显示
          const formattedDistance = this.formatDistance(distanceValue);

          // 确保其他必要字段存在
          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: distanceValue,
            formattedDistance: formattedDistance,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        // 使用处理后的场馆数据
        that.setData({
          venues: processedVenues,
          loading: false
        });

        // 更新场馆距离
        if (that.data.latitude && that.data.longitude) {
          that.updateVenueDistance(that.data.latitude, that.data.longitude);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('搜索场馆失败', err);
        wx.showToast({
          title: '搜索场馆失败',
          icon: 'none'
        });
      });
  },

  // 场馆卡片点击事件
  onVenueCardTap: function(e) {
    const venueId = e.currentTarget.dataset.id;
    // 导航到场馆详情页
    wx.navigateTo({
      url: `/pages/venue-detail/venue-detail?id=${venueId}`
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 切换筛选下拉菜单
  toggleFilterDropdown: function() {
    this.setData({
      showFilterDropdown: !this.data.showFilterDropdown,
      showSortDropdown: false,
      showSportTypeDropdown: false
    });
  },

  // 选择场馆类型
  selectVenueType: function(e) {
    const typeId = e.currentTarget.dataset.id;
    const venueTypes = this.data.venueTypes.map(item => {
      if (typeId === 'all') {
        // 如果选择"全部"，则只有"全部"为选中状态
        return {
          ...item,
          selected: item.id === 'all'
        };
      } else {
        // 如果选择具体类型，则"全部"取消选中，选中对应类型
        if (item.id === 'all') {
          return {
            ...item,
            selected: false
          };
        } else if (item.id === typeId) {
          return {
            ...item,
            selected: !item.selected
          };
        } else {
          return item;
        }
      }
    });

    // 获取选中的场馆类型ID
    const selectedVenueTypes = venueTypes
      .filter(item => item.selected && item.id !== 'all')
      .map(item => item.id);

    this.setData({
      venueTypes,
      selectedVenueTypes
    });

    // 如果没有选中任何类型，则自动选中"全部"
    if (selectedVenueTypes.length === 0) {
      this.setData({
        venueTypes: venueTypes.map(item => {
          if (item.id === 'all') {
            return {
              ...item,
              selected: true
            };
          }
          return item;
        })
      });
    }

    // 根据选中的场馆类型筛选场馆
    this.filterVenuesByType(selectedVenueTypes);
  },

  // 根据场馆类型筛选场馆
  filterVenuesByType: function(venueTypeIds) {
    // 如果没有选中任何类型或选中了"全部"，则显示所有场馆
    if (venueTypeIds.length === 0) {
      this.loadVenueData();
      return;
    }

    wx.showLoading({
      title: '筛选中...',
    });

    // 调用API筛选场馆
    api.getVenues()
      .then(res => {
        wx.hideLoading();
        const venuesData = res.data || [];

        // 根据场馆类型筛选场馆
        const filteredVenues = venuesData.filter(venue => {
          if (!venue.type) {
            return false;
          }

          // 检查场馆是否属于选中的类型
          return venueTypeIds.some(typeId => {
            // 将typeId转换为对应的场馆类型名称
            const typeName = this.getVenueTypeById(typeId);
            return venue.type.includes(typeName);
          });
        });

        if (filteredVenues.length === 0) {
          wx.showToast({
            title: '未找到相关场馆',
            icon: 'none'
          });
          this.setData({
            venues: [],
            loading: false
          });
          return;
        }

        // 处理场馆数据
        const processedVenues = filteredVenues.map(venue => {
          const distanceValue = venue.distance || 0;
          // 格式化距离显示
          const formattedDistance = this.formatDistance(distanceValue);

          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: distanceValue,
            formattedDistance: formattedDistance,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        this.setData({
          venues: processedVenues,
          loading: false
        });

        // 更新场馆距离
        if (this.data.latitude && this.data.longitude) {
          this.updateVenueDistance(this.data.latitude, this.data.longitude);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('筛选场馆失败', err);
        wx.showToast({
          title: '筛选场馆失败',
          icon: 'none'
        });
      });
  },

  // 根据ID获取场馆类型名称
  getVenueTypeById: function(typeId) {
    const venueTypeMap = {
      'national': '国家社区运动中心',
      'campus': '校园场地',
      'public': '公共场地',
      'commercial': '商业体育'
    };

    return venueTypeMap[typeId] || '';
  },

  // 选择排序方式
  selectSortOption: function(e) {
    const sortOption = e.currentTarget.dataset.id;
    this.setData({
      selectedSortOption: sortOption,
      showSortDropdown: false
    });

    // 根据排序方式重新排序
    this.sortVenues(sortOption);
  },

  // 选择运动类型
  selectSportType: function(e) {
    const typeId = e.currentTarget.dataset.id;
    const sportTypes = this.data.sportTypes.map(item => {
      if (typeId === 'all') {
        // 如果选择"全部"，则只有"全部"为选中状态
        return {
          ...item,
          selected: item.id === 'all'
        };
      } else {
        // 如果选择具体类型，则"全部"取消选中，选中对应类型
        if (item.id === 'all') {
          return {
            ...item,
            selected: false
          };
        } else if (item.id === typeId) {
          return {
            ...item,
            selected: !item.selected
          };
        } else {
          return item;
        }
      }
    });

    // 获取选中的运动类型ID
    const selectedSportTypes = sportTypes
      .filter(item => item.selected && item.id !== 'all')
      .map(item => item.id);

    this.setData({
      sportTypes,
      selectedSportTypes
    });

    // 如果没有选中任何类型，则自动选中"全部"
    if (selectedSportTypes.length === 0) {
      this.setData({
        sportTypes: sportTypes.map(item => {
          if (item.id === 'all') {
            return {
              ...item,
              selected: true
            };
          }
          return item;
        })
      });
    }

    // 根据选中的运动类型筛选场馆
    this.filterVenuesBySportType(selectedSportTypes);
  },

  // 根据运动类型筛选场馆
  filterVenuesBySportType: function(sportTypeIds) {
    // 如果没有选中任何类型或选中了"全部"，则显示所有场馆
    if (sportTypeIds.length === 0) {
      this.loadVenueData();
      return;
    }

    wx.showLoading({
      title: '筛选中...',
    });

    // 调用API筛选场馆
    api.getVenues()
      .then(res => {
        wx.hideLoading();
        const venuesData = res.data || [];

        // 根据运动类型筛选场馆
        const filteredVenues = venuesData.filter(venue => {
          if (!venue.sports || venue.sports.length === 0) {
            return false;
          }

          // 检查场馆是否包含选中的运动类型
          return sportTypeIds.some(typeId => {
            // 将typeId转换为对应的运动名称
            const sportName = this.getSportNameById(typeId);
            return venue.sports.includes(sportName);
          });
        });

        if (filteredVenues.length === 0) {
          wx.showToast({
            title: '未找到相关场馆',
            icon: 'none'
          });
          this.setData({
            venues: [],
            loading: false
          });
          return;
        }

        // 处理场馆数据
        const processedVenues = filteredVenues.map(venue => {
          const distanceValue = venue.distance || 0;
          // 格式化距离显示
          const formattedDistance = this.formatDistance(distanceValue);

          return {
            ...venue,
            name: venue.name || '未命名场馆',
            type: venue.type || '未分类',
            distance: distanceValue,
            formattedDistance: formattedDistance,
            imageUrl: venue.imageUrl || 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg'
          };
        });

        this.setData({
          venues: processedVenues,
          loading: false
        });

        // 更新场馆距离
        if (this.data.latitude && this.data.longitude) {
          this.updateVenueDistance(this.data.latitude, this.data.longitude);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('筛选场馆失败', err);
        wx.showToast({
          title: '筛选场馆失败',
          icon: 'none'
        });
      });
  },

  // 根据ID获取运动类型名称
  getSportNameById: function(typeId) {
    const sportTypeMap = {
      'badminton': '羽毛球',
      'basketball': '篮球',
      'football': '足球',
      'tennis': '网球',
      'pingpong': '乒乓球',
      'swimming': '游泳',
      'fitness': '健身',
      'yoga': '瑜伽',
      'volleyball': '排球',
      'billiards': '台球',
      'dance': '舞蹈'
    };

    return sportTypeMap[typeId] || '';
  },

  // 根据选项排序场馆
  sortVenues: function(sortOption) {
    const venues = [...this.data.venues];

    if (sortOption === 'distance') {
      // 按距离排序
      venues.sort((a, b) => a.distance - b.distance);
    } else if (sortOption === 'popularity') {
      // 按人气排序（这里简单模拟，实际应该有人气数据）
      venues.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
    } else if (sortOption === 'comprehensive') {
      // 综合排序（这里简单实现为按距离和人气的加权排序）
      venues.sort((a, b) => {
        const aScore = (a.distance || 0) * 0.6 + (a.popularity || 0) * 0.4;
        const bScore = (b.distance || 0) * 0.6 + (b.popularity || 0) * 0.4;
        return aScore - bScore;
      });
    }

    this.setData({
      venues
    });
  },

  // 点击页面空白处关闭下拉菜单
  onTapBackground: function() {
    if (this.data.showSortDropdown || this.data.showSportTypeDropdown || this.data.showFilterDropdown) {
      this.setData({
        showSortDropdown: false,
        showSportTypeDropdown: false,
        showFilterDropdown: false
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡
    return false;
  },

  // 分享
  onShareAppMessage: function() {
    return {
      title: '万众运动场馆列表',
      path: '/pages/venue-list/venue-list'
    };
  }
});
