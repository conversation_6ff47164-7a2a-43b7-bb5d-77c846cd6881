<!--index.wxml-->
<view class="map-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title-container">
      <image src="/images/union-logo.png" mode="heightFix" class="nav-logo"></image>
      <view class="nav-title">大桥镇15分钟职工文化服务圈</view>
    </view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 顶部搜索框 -->
  <view class="search-box">
    <view class="search-input">
      <icon type="search" size="14" color="#999"></icon>
      <input placeholder="搜索场地名称/场地标签/运动类型" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" value="{{searchKeyword}}" />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 地图组件 -->
  <map id="venueMap"
    longitude="{{longitude}}"
    latitude="{{latitude}}"
    scale="{{scale}}"
    markers="{{markers}}"
    bindmarkertap="onMarkerTap"
    show-location="true"
    enable-overlooking="true"
    enable-rotate="true"
    enable-3D="true"
    enable-zoom="true"
    enable-scroll="true"
    style="width: 100%; height: 100%;">
  </map>

  <!-- 查看列表按钮 -->
  <view class="list-toggle" bindtap="toggleListView">
    <view class="list-icon"></view>
    <text>场地列表</text>
  </view>



  <!-- 底部场地列表 -->
  <view class="venue-list-container">
    <swiper
      id="venueSwiper"
      class="venue-swiper"
      indicator-dots="{{false}}"
      autoplay="{{false}}"
      circular="{{true}}"
      current="{{currentSwiperIndex}}"
      previous-margin="120rpx"
      next-margin="120rpx"
      snap-to-edge="{{false}}"
      display-multiple-items="1"
      easing-function="easeInOutCubic"
      bindchange="onVenueSwiperChange">
      <swiper-item wx:for="{{venues}}" wx:key="id" class="venue-swiper-item">
        <view
          id="venue-{{item.id}}"
          class="venue-card {{selectedVenueId === item.id ? 'active' : ''}}"
          bindtap="onVenueCardTap"
          data-id="{{item.id}}">
          <image src="{{item.imageUrl}}" mode="aspectFill" class="venue-image"></image>
          <!-- 黑色渐变蒙版 -->
          <view class="venue-overlay"></view>
          <!-- 场地信息 -->
          <view class="venue-info">
            <view class="venue-name" wx:if="{{item.name}}">{{item.name}}</view>
            <view class="venue-name" wx:else>未命名场馆</view>
            <view class="venue-tags">
              <view class="venue-tag" wx:if="{{item.type}}">{{item.type}}</view>
              <view class="venue-tag" wx:else>未分类</view>
              <!-- 运动类型标签 -->
              <view class="venue-tag sport-tag" wx:if="{{item.sports && item.sports.length > 0}}">{{item.sports[0]}}</view>
              <view class="venue-tag sport-tag" wx:if="{{item.sports && item.sports.length > 1}}">{{item.sports[1]}}</view>
              <!-- 如果有更多运动类型，显示+X -->
              <view class="venue-tag sport-tag more-tag" wx:if="{{item.sports && item.sports.length > 2}}">+{{item.sports.length - 2}}</view>
            </view>
          </view>
          <!-- 距离显示 -->
          <view class="venue-distance">
            <icon type="location" size="14" color="#fff"></icon>
            <text>{{item.distance}}米</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 定位按钮 -->
  <view class="location-button" bindtap="moveToLocation">
    <view class="location-icon">
      <view class="icon-location"></view>
    </view>
  </view>

  <!-- 认证按钮 -->
  <view class="auth-button" bindtap="navigateToOrganizations">
    <view class="auth-icon">
      <view class="icon-auth"></view>
    </view>
    <text class="button-label">认证</text>
  </view>

  <!-- 我的订单按钮 -->
  <view class="my-orders-button" bindtap="navigateToMyOrders">
    <view class="my-orders-icon">
      <view class="icon-document"></view>
    </view>
    <text class="button-label">订单</text>
  </view>

  <!-- 运动社团按钮 -->
  <view class="club-button" bindtap="navigateToClubs">
    <view class="club-icon">
      <view class="icon-club"></view>
    </view>
    <text class="button-label">社团</text>
  </view>

  <!-- 我的按钮 -->
  <view class="my-button" bindtap="navigateToMy">
    <view class="my-icon">
      <view class="icon-my"></view>
    </view>
    <text class="button-label">我的</text>
  </view>
</view>
