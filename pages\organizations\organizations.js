// pages/organizations/organizations.js
import { getCertifications, cancelCertification } from '../../utils/storage';

Page({
  data: {
    organizations: [],
    filteredOrganizations: [],
    loading: true,
    searchKeyword: '',
    currentTab: 'all', // 当前选中的标签: all, enterprise, institution
    certifications: [], // 用户的认证记录

    // 模拟数据
    mockOrganizations: [
      {
        id: 1,
        name: '浙江清华长三角研究院',
        type: 'institution', // 事业单位
        address: '浙江省嘉兴市南湖区亚太路705号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江清华长三角研究院是清华大学在长三角地区设立的研究机构，致力于科技创新和产业发展。'
      },
      {
        id: 2,
        name: '阿里巴巴集团',
        type: 'enterprise', // 企业单位
        address: '浙江省杭州市余杭区文一西路969号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '阿里巴巴集团是全球领先的电子商务和科技公司，提供各类互联网服务。'
      },
      {
        id: 3,
        name: '浙江大学',
        type: 'institution',
        address: '浙江省杭州市西湖区余杭塘路866号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江大学是中国顶尖的高等教育机构之一，在科研和教学方面享有盛誉。'
      },
      {
        id: 4,
        name: '海康威视',
        type: 'enterprise',
        address: '浙江省杭州市滨江区阡陌路555号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '海康威视是全球领先的安防产品和解决方案提供商，专注于视频监控技术。'
      },
      {
        id: 5,
        name: '浙江省人民医院',
        type: 'institution',
        address: '浙江省杭州市上城区庆春路158号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江省人民医院是浙江省规模最大的综合性医院之一，提供全面的医疗服务。'
      }
    ]
  },

  onLoad: function() {
    this.loadOrganizations();
  },

  onShow: function() {
    // 每次页面显示时重新加载认证记录和组织列表
    this.loadCertifications();
    this.loadOrganizations();
  },

  // 加载认证记录
  loadCertifications: function() {
    // 获取用户的认证记录
    const certifications = getCertifications();

    this.setData({
      certifications
    });
  },

  // 加载企事业单位数据
  loadOrganizations: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 使用模拟数据
    const organizations = this.data.mockOrganizations;
    const { certifications } = this.data;

    // 为每个组织添加认证状态
    const orgsWithCertStatus = organizations.map(org => {
      // 查找该组织的认证记录
      const certification = certifications.find(cert => cert.orgId === org.id.toString());

      return {
        ...org,
        certified: !!certification,
        certification: certification || null
      };
    });

    this.setData({
      organizations: orgsWithCertStatus,
      filteredOrganizations: orgsWithCertStatus,
      loading: false,
      currentTab: 'all'
    });

    wx.hideLoading();
  },

  // 搜索企事业单位
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });

    // 输入时自动搜索
    this.doSearch();
  },

  // 清除搜索关键词
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.doSearch();
  },

  // 执行搜索
  doSearch: function() {
    const keyword = this.data.searchKeyword.toLowerCase();
    let filtered = this.data.organizations;

    if (keyword) {
      filtered = filtered.filter(org =>
        org.name.toLowerCase().includes(keyword) ||
        org.description.toLowerCase().includes(keyword)
      );
    }

    // 应用当前标签筛选
    if (this.data.currentTab !== 'all') {
      filtered = filtered.filter(org => org.type === this.data.currentTab);
    }

    this.setData({
      filteredOrganizations: filtered
    });
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab === this.data.currentTab) return;

    let filteredOrganizations = this.data.organizations;

    // 应用搜索关键词筛选
    const keyword = this.data.searchKeyword.toLowerCase();
    if (keyword) {
      filteredOrganizations = filteredOrganizations.filter(org =>
        org.name.toLowerCase().includes(keyword) ||
        org.description.toLowerCase().includes(keyword)
      );
    }

    // 应用标签筛选
    if (tab !== 'all') {
      filteredOrganizations = filteredOrganizations.filter(org => org.type === tab);
    }

    this.setData({
      currentTab: tab,
      filteredOrganizations
    });
  },

  // 申请认证或取消认证
  applyForCertification: function(e) {
    const orgId = e.currentTarget.dataset.id;
    const organization = this.data.organizations.find(org => org.id === orgId);

    if (!organization) return;

    // 如果已认证，则取消认证
    if (organization.certified) {
      wx.showModal({
        title: '取消认证',
        content: `确定要取消与"${organization.name}"的认证关系吗？`,
        confirmText: '确定取消',
        confirmColor: '#E74C3C',
        success: (res) => {
          if (res.confirm) {
            // 执行取消认证
            const result = cancelCertification(orgId.toString());

            if (result) {
              wx.showToast({
                title: '已取消认证',
                icon: 'success'
              });

              // 重新加载数据
              this.loadCertifications();
              this.loadOrganizations();
            } else {
              wx.showToast({
                title: '取消认证失败',
                icon: 'none'
              });
            }
          }
        }
      });
    } else {
      // 如果未认证，则申请认证
      wx.navigateTo({
        url: `/pages/certification/certification?orgId=${orgId}&orgName=${organization.name}&orgType=${organization.type}`
      });
    }
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
});
