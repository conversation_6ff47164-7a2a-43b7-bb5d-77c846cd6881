<!--pages/clubs/clubs.wxml-->
<view class="clubs-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">运动社团</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 搜索框 -->
  <view class="search-box">
    <view class="search-input-container">
      <icon type="search" size="14" color="#999"></icon>
      <input
        class="search-input"
        placeholder="搜索社团名称/运动类型/单位名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="doSearch"
        confirm-type="search"
      />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态提示 -->
    <view class="empty-container" wx:if="{{!loading && filteredClubs.length === 0}}">
      <view class="empty-icon">🏆</view>
      <view class="empty-text">暂无社团数据</view>
      <view class="empty-subtext">创建一个新的运动社团吧！</view>
    </view>

    <!-- 社团列表 -->
    <scroll-view scroll-y="true" class="clubs-list" wx:if="{{!loading && filteredClubs.length > 0}}">
      <view class="club-item" wx:for="{{filteredClubs}}" wx:key="id">
        <!-- 社团信息 -->
        <view class="club-info" bindtap="navigateToClubDetail" data-id="{{item.id}}">
          <view class="club-logo-container">
            <image class="club-logo" src="{{item.logo}}" mode="aspectFill" wx:if="{{item.logo}}"></image>
            <view class="club-logo-placeholder" wx:else>社团</view>
            <view class="club-member-count">
              <text>成员：{{item.memberCount}}人</text>
            </view>
          </view>
          <view class="club-details">
            <view class="club-name-container">
              <view class="club-name">{{item.name}}</view>
              <view class="club-creator-badge" wx:if="{{item.isCreator}}">创建者</view>
              <view class="club-member-badge" wx:elif="{{item.isMember}}">成员</view>
              <view class="club-pending-badge" wx:elif="{{item.isPending}}">审核中</view>
            </view>
            <view class="club-org">所属单位：{{item.orgName}}</view>
            <view class="club-sports">
              <text wx:for="{{item.sportTypes}}" wx:for-item="sport" wx:key="*this" class="sport-tag">{{sport}}</text>
            </view>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="club-actions" catchtap="preventBubble">
          <!-- 未加入显示申请加入按钮 -->
          <button class="join-button" wx:if="{{!item.isCreator && !item.isMember && !item.isPending}}" catchtap="joinClub" data-id="{{item.id}}">
            申请加入
          </button>

          <!-- 已申请加入但未审批 -->
          <button class="cancel-button" wx:if="{{!item.isCreator && !item.isMember && item.isPending}}" catchtap="cancelJoinRequest" data-id="{{item.id}}">取消审核</button>

          <!-- 已加入或创建者显示进入社团按钮 -->
          <button class="enter-button" wx:if="{{item.isCreator || item.isMember}}" catchtap="navigateToClubDetail" data-id="{{item.id}}">进入社团</button>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 创建社团按钮 -->
  <view class="create-club-button" bindtap="navigateToCreateClub">
    <text>+</text>
  </view>
</view>