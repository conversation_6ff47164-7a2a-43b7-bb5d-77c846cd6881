// pages/club-detail/club-detail.js
import {
  getClubById,
  updateClub,
  approveClubMember,
  updateClubMemberRole,
  leaveClub,
  addClubAnnouncement,
  addClubActivity,
  likeClubActivity,
  commentClubActivity
} from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    clubId: null,
    club: null,
    loading: true,
    isCreator: false,
    isMember: false,
    isAdmin: false,
    currentTab: 'members', // 当前选中的标签: members, announcements, activities
    members: [],
    pendingMembers: [],
    announcements: [],
    activities: [],

    // 弹窗相关
    showAnnouncementModal: false,
    announcementTitle: '',
    announcementContent: '',

    showActivityModal: false,
    activityContent: '',

    showCommentInput: false,
    commentContent: '',
    currentActivityId: null,

    userId: 'user_123' // 使用固定ID以便与模拟数据中的创建者ID匹配
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({
        clubId: options.id
      });
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '缺少社团ID',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果已经加载过社团详情，重新加载以获取最新数据
    if (this.data.clubId) {
      this.loadClubDetail();
    }
  },

  // 加载社团详情
  loadClubDetail() {
    wx.showLoading({
      title: '加载中...',
    });

    // 获取社团详情
    const club = getClubById(this.data.clubId);

    if (!club) {
      wx.hideLoading();
      wx.showToast({
        title: '社团不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 检查当前用户是否是创建者
    const isCreator = club.creatorId === this.data.userId;

    // 检查当前用户是否是成员
    const isMember = club.members ? club.members.some(m => m.id === this.data.userId && m.status === 'approved') : false;

    // 检查当前用户是否是管理员
    const isAdmin = club.members ? club.members.some(m =>
      m.id === this.data.userId &&
      m.status === 'approved' &&
      m.role === 'admin'
    ) : false;

    // 处理成员数据
    const members = club.members ? club.members.filter(m => m.status === 'approved').map(m => {
      return {
        ...m,
        joinTimeFormatted: this.formatTime(m.joinedAt),
        isCreator: m.id === club.creatorId,
        isAdmin: m.role === 'admin',
        isRegularMember: m.id !== club.creatorId && m.role !== 'admin'
      };
    }) : [];

    // 处理待审批成员数据
    const pendingMembers = club.members ? club.members.filter(m => m.status === 'pending').map(m => {
      return {
        ...m,
        joinTimeFormatted: this.formatTime(m.joinedAt)
      };
    }) : [];

    // 处理公告数据
    const announcements = club.announcements ? club.announcements.map(a => {
      return {
        ...a,
        timeFormatted: this.formatTime(a.createdAt)
      };
    }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) : [];

    // 处理动态数据
    const activities = club.activities ? club.activities.map(a => {
      // 查找发布者信息
      const member = club.members.find(m => m.id === a.userId);

      return {
        ...a,
        timeFormatted: this.formatTime(a.createdAt),
        userAvatar: member ? member.avatar : 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        userName: member ? member.name : '未知用户',
        likeCount: a.likes ? a.likes.length : 0,
        commentCount: a.comments ? a.comments.length : 0,
        hasLiked: a.likes ? a.likes.includes(this.data.userId) : false,
        comments: a.comments ? a.comments.map(c => {
          // 查找评论者信息
          const commentMember = club.members.find(m => m.id === c.userId);

          return {
            ...c,
            timeFormatted: this.formatTime(c.createdAt),
            userName: commentMember ? commentMember.name : '未知用户'
          };
        }) : []
      };
    }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) : [];

    // 计算成员数量
    const memberCount = members.length;

    this.setData({
      club: {
        ...club,
        memberCount
      },
      isCreator,
      isMember,
      isAdmin,
      members,
      pendingMembers,
      announcements,
      activities,
      loading: false
    });

    wx.hideLoading();
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';

    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;

    // 小于1分钟
    if (diff < 60 * 1000) {
      return '刚刚';
    }

    // 小于1小时
    if (diff < 60 * 60 * 1000) {
      return Math.floor(diff / (60 * 1000)) + '分钟前';
    }

    // 小于1天
    if (diff < 24 * 60 * 60 * 1000) {
      return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
    }

    // 小于1周
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
    }

    // 格式化日期
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day} ${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}`;
  },

  // 切换选项卡
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 审批成员
  approveMember(e) {
    const memberId = e.currentTarget.dataset.id;

    wx.showLoading({
      title: '处理中...',
    });

    // 审批成员
    const success = approveClubMember(this.data.clubId, memberId, 'approved');

    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '已通过',
        icon: 'success'
      });

      // 重新加载社团详情
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 拒绝成员
  rejectMember(e) {
    const memberId = e.currentTarget.dataset.id;

    wx.showLoading({
      title: '处理中...',
    });

    // 拒绝成员
    const success = approveClubMember(this.data.clubId, memberId, 'rejected');

    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '已拒绝',
        icon: 'success'
      });

      // 重新加载社团详情
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 显示公告弹窗
  showAnnouncementModal() {
    this.setData({
      showAnnouncementModal: true,
      announcementTitle: '',
      announcementContent: ''
    });
  },

  // 隐藏公告弹窗
  hideAnnouncementModal() {
    this.setData({
      showAnnouncementModal: false
    });
  },

  // 公告标题输入事件
  onAnnouncementTitleInput(e) {
    this.setData({
      announcementTitle: e.detail.value
    });
  },

  // 公告内容输入事件
  onAnnouncementContentInput(e) {
    this.setData({
      announcementContent: e.detail.value
    });
  },

  // 发布公告
  publishAnnouncement() {
    const { announcementTitle, announcementContent } = this.data;

    if (!announcementTitle || !announcementContent) {
      return;
    }

    wx.showLoading({
      title: '发布中...',
    });

    // 创建公告数据
    const announcement = {
      title: announcementTitle,
      content: announcementContent,
      userId: this.data.userId
    };

    // 发布公告
    const success = addClubAnnouncement(this.data.clubId, announcement);

    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });

      // 隐藏弹窗
      this.hideAnnouncementModal();

      // 重新加载社团详情
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '发布失败',
        icon: 'none'
      });
    }
  },

  // 显示动态弹窗
  showActivityModal() {
    this.setData({
      showActivityModal: true,
      activityContent: ''
    });
  },

  // 隐藏动态弹窗
  hideActivityModal() {
    this.setData({
      showActivityModal: false
    });
  },

  // 动态内容输入事件
  onActivityContentInput(e) {
    this.setData({
      activityContent: e.detail.value
    });
  },

  // 发布动态
  publishActivity() {
    const { activityContent } = this.data;

    if (!activityContent) {
      return;
    }

    wx.showLoading({
      title: '发布中...',
    });

    // 创建动态数据
    const activity = {
      content: activityContent,
      userId: this.data.userId,
      images: [] // 暂不支持图片上传
    };

    // 发布动态
    const activityId = addClubActivity(this.data.clubId, activity);

    wx.hideLoading();

    if (activityId) {
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });

      // 隐藏弹窗
      this.hideActivityModal();

      // 重新加载社团详情
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '发布失败',
        icon: 'none'
      });
    }
  },

  // 点赞/取消点赞
  toggleLike(e) {
    const activityId = e.currentTarget.dataset.id;

    // 点赞/取消点赞
    const success = likeClubActivity(this.data.clubId, activityId, this.data.userId);

    if (success) {
      // 重新加载社团详情
      this.loadClubDetail();
    }
  },

  // 显示评论输入框
  showCommentInput(e) {
    const activityId = e.currentTarget.dataset.id;

    this.setData({
      showCommentInput: true,
      currentActivityId: activityId,
      commentContent: ''
    });
  },

  // 隐藏评论输入框
  hideCommentInput() {
    this.setData({
      showCommentInput: false
    });
  },

  // 评论内容输入事件
  onCommentContentInput(e) {
    this.setData({
      commentContent: e.detail.value
    });
  },

  // 提交评论
  submitComment() {
    const { commentContent, currentActivityId } = this.data;

    if (!commentContent || !currentActivityId) {
      return;
    }

    // 创建评论数据
    const comment = {
      content: commentContent,
      userId: this.data.userId
    };

    // 发表评论
    const success = commentClubActivity(this.data.clubId, currentActivityId, comment);

    if (success) {
      // 隐藏评论输入框
      this.hideCommentInput();

      // 重新加载社团详情
      this.loadClubDetail();
    } else {
      wx.showToast({
        title: '评论失败',
        icon: 'none'
      });
    }
  },

  // 预览图片
  previewImage(e) {
    const { current, urls } = e.currentTarget.dataset;

    wx.previewImage({
      current,
      urls
    });
  },

  // 设置成员为管理员
  setMemberAsAdmin(e) {
    const memberId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '设置管理员',
      content: '确定将该成员设置为管理员吗？管理员可以管理社团成员和审批加入申请。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          // 设置成员为管理员
          const success = updateClubMemberRole(this.data.clubId, memberId, 'admin');

          wx.hideLoading();

          if (success) {
            wx.showToast({
              title: '已设置为管理员',
              icon: 'success'
            });

            // 重新加载社团详情
            this.loadClubDetail();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 取消管理员身份
  removeMemberAsAdmin(e) {
    const memberId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '取消管理员',
      content: '确定取消该成员的管理员身份吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          // 取消成员的管理员身份
          const success = updateClubMemberRole(this.data.clubId, memberId, 'member');

          wx.hideLoading();

          if (success) {
            wx.showToast({
              title: '已取消管理员身份',
              icon: 'success'
            });

            // 重新加载社团详情
            this.loadClubDetail();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 移除成员
  removeMember(e) {
    const memberId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '移除成员',
      content: '确定将该成员从社团中移除吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          // 移除成员
          const success = leaveClub(this.data.clubId, memberId);

          wx.hideLoading();

          if (success) {
            wx.showToast({
              title: '已移除成员',
              icon: 'success'
            });

            // 重新加载社团详情
            this.loadClubDetail();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 导航到社团管理页面
  navigateToManagement() {
    wx.navigateTo({
      url: `/pages/club-management/club-management?id=${this.data.clubId}`
    });
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  }
})