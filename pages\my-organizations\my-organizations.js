// pages/my-organizations/my-organizations.js
import { getCertifications } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    certifications: [],
    organizations: [],
    
    // 模拟企事业单位数据
    mockOrganizations: [
      {
        id: '1',
        name: '浙江大学',
        type: 'institution',
        address: '浙江省杭州市西湖区余杭塘路866号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江大学是中国著名的综合性研究型大学，位于浙江省杭州市。'
      },
      {
        id: '2',
        name: '阿里巴巴',
        type: 'enterprise',
        address: '浙江省杭州市余杭区文一西路969号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '阿里巴巴集团是全球领先的电子商务公司，提供各种互联网服务。'
      },
      {
        id: '3',
        name: '杭州市第一人民医院',
        type: 'institution',
        address: '浙江省杭州市上城区浣纱路261号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '杭州市第一人民医院是杭州市规模最大的综合性医院之一，提供全面的医疗服务。'
      },
      {
        id: '4',
        name: '海康威视',
        type: 'enterprise',
        address: '浙江省杭州市滨江区阡陌路555号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '海康威视是全球领先的安防产品和解决方案提供商，专注于视频监控技术。'
      },
      {
        id: '5',
        name: '浙江省人民医院',
        type: 'institution',
        address: '浙江省杭州市上城区庆春路158号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '浙江省人民医院是浙江省规模最大的综合性医院之一，提供全面的医疗服务。'
      },
      {
        id: '6',
        name: '清华长三角研究院',
        type: 'institution',
        address: '浙江省嘉兴市南湖区亚太路1288号',
        logo: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
        description: '清华长三角研究院是清华大学在长三角地区设立的研究机构，致力于科技创新和产业发展。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 每次页面显示时重新加载数据，确保数据最新
    this.loadData();
  },

  // 加载数据
  loadData: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 获取用户的认证记录
    const certifications = getCertifications();
    
    // 使用模拟数据
    const allOrganizations = this.data.mockOrganizations;
    
    // 筛选出用户已认证的单位
    const userOrganizations = [];
    
    certifications.forEach(cert => {
      const org = allOrganizations.find(o => o.id.toString() === cert.orgId.toString());
      if (org) {
        userOrganizations.push({
          ...org,
          certification: cert
        });
      }
    });

    this.setData({
      certifications,
      organizations: userOrganizations,
      loading: false
    });

    wx.hideLoading();
  },

  // 查看认证详情
  viewCertification: function(e) {
    const orgId = e.currentTarget.dataset.id;
    const org = this.data.organizations.find(o => o.id.toString() === orgId.toString());
    
    if (!org) return;
    
    wx.showModal({
      title: '认证信息',
      content: `您已成功认证"${org.name}"，可享受该单位相关场馆的预订折扣。`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 取消认证
  cancelCertification: function(e) {
    const orgId = e.currentTarget.dataset.id;
    const org = this.data.organizations.find(o => o.id.toString() === orgId.toString());
    
    if (!org) return;
    
    wx.showModal({
      title: '取消认证',
      content: `确定要取消"${org.name}"的认证吗？取消后将无法享受相关折扣。`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用取消认证的API
          const { cancelCertification } = require('../../utils/storage');
          const success = cancelCertification(orgId);
          
          if (success) {
            wx.showToast({
              title: '已取消认证',
              icon: 'success'
            });
            
            // 重新加载数据
            this.loadData();
          } else {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 申请新认证
  applyNewCertification: function() {
    wx.navigateTo({
      url: '/pages/organizations/organizations'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
})
