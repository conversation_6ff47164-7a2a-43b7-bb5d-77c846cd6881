// pages/my/my.js
import { getOrders, getCertifications, getClubs } from '../../utils/storage';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userId: 'user_123', // 使用固定ID以便与模拟数据中的创建者ID匹配
    userInfo: {
      name: '张三',
      avatar: 'https://sport-health-file.allelink.com.cn/20240829/6b2cc79d336b4397b8f9c3f4891f880e.jpg',
      phone: '135****0573'
    },
    certifications: [],
    orderCount: 0,
    clubCount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadUserData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次页面显示时重新加载数据，确保数据最新
    this.loadUserData();
  },

  // 加载用户数据
  loadUserData: function() {
    // 获取认证信息
    const certifications = getCertifications();

    // 获取订单数量
    const orders = getOrders();

    // 获取用户加入的社团
    const clubs = getClubs();
    const userClubs = clubs.filter(club =>
      club.members &&
      club.members.some(m =>
        m.id === this.data.userId &&
        m.status === 'approved'
      )
    );

    this.setData({
      certifications,
      orderCount: orders.length,
      clubCount: userClubs.length
    });
  },

  // 导航到我的订单页面
  navigateToMyOrders: function() {
    wx.navigateTo({
      url: '/pages/my-orders/my-orders'
    });
  },

  // 导航到我的单位页面
  navigateToMyOrganizations: function() {
    wx.navigateTo({
      url: '/pages/my-organizations/my-organizations'
    });
  },

  // 导航到我的社团页面
  navigateToMyClubs: function() {
    wx.navigateTo({
      url: '/pages/my-clubs/my-clubs'
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
})
