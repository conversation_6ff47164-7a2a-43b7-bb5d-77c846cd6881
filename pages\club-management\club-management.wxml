<!--pages/club-management/club-management.wxml-->
<view class="management-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="navigateBack">
      <view class="back-icon">←</view>
    </view>
    <view class="nav-title">社团管理</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container" wx:if="{{!loading && club}}">
    <form bindsubmit="submitForm">
      <!-- 社团名称 -->
      <view class="form-item">
        <view class="form-label">社团名称</view>
        <input
          class="form-input"
          name="name"
          placeholder="请输入社团名称"
          value="{{formData.name}}"
          bindinput="onNameInput"
        />
      </view>

      <!-- 运动类型 -->
      <view class="form-item">
        <view class="form-label">运动类型</view>
        <view class="sport-types-container">
          <view
            class="sport-type-item {{item.selected ? 'selected' : ''}}"
            wx:for="{{sportTypes}}"
            wx:key="name"
            bindtap="toggleSportType"
            data-index="{{index}}"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <!-- 社团介绍 -->
      <view class="form-item">
        <view class="form-label">社团介绍</view>
        <textarea
          class="form-textarea"
          name="description"
          placeholder="请输入社团介绍"
          value="{{formData.description}}"
          bindinput="onDescriptionInput"
          maxlength="200"
        ></textarea>
        <view class="textarea-counter">{{formData.description.length}}/200</view>
      </view>

      <!-- 提交按钮 -->
      <view class="form-button-container">
        <button
          class="submit-button {{formValid ? '' : 'disabled'}}"
          form-type="submit"
          disabled="{{!formValid}}"
        >保存修改</button>
      </view>
    </form>
  </view>
</view>