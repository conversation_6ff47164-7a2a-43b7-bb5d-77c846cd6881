// pages/order-detail/order-detail.js
import * as api from '../../utils/api';
import { getOrderById, updateOrderById } from '../../utils/storage';

Page({
  data: {
    orderId: null,
    order: null,
    loading: true,

    // 用户头像数组（包括自己和好友）
    participants: [],

    // 控制更多选项气泡显示
    showMoreOptions: false,

    // 签到相关
    showSignInModal: false,
    currentSession: null,
    countDown: 5,
    canSignIn: false
  },

  onLoad: function(options) {
    // 获取传递的订单ID
    const orderId = options.orderId;
    const action = options.action; // 可能的值: scan

    if (!orderId) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orderId
    });

    // 加载订单详情
    this.loadOrderDetail(orderId, () => {
      // 如果指定了action=scan，加载完成后自动触发扫码签到
      if (action === 'scan') {
        setTimeout(() => {
          this.scanToEnter();
        }, 500);
      }
    });

    // 设置页面不可滚动
    wx.setPageStyle({
      style: {
        overflow: 'hidden'
      }
    });
  },

  // 加载订单详情
  loadOrderDetail: function(orderId, callback) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 从本地存储获取订单数据
    const orderData = getOrderById(orderId);

    if (orderData) {
      // 如果本地存储中有订单数据，直接使用
      this.setData({
        order: orderData,
        participants: orderData.participants || [],
        loading: false
      });
      wx.hideLoading();

      // 如果有回调函数，执行回调
      if (typeof callback === 'function') {
        callback();
      }
    } else {
      // 如果本地存储中没有订单数据，则从API获取
      // 注意：这里是为了兼容之前的逻辑，实际项目中可能直接从服务器获取
      api.getOrderDetail(orderId)
        .then(res => {
          if (res.code === 0 && res.data) {
            this.setData({
              order: res.data,
              participants: res.data.participants || [],
              loading: false
            });
          } else {
            wx.showToast({
              title: '获取订单详情失败',
              icon: 'none'
            });
          }
          wx.hideLoading();

          // 如果有回调函数，执行回调
          if (typeof callback === 'function') {
            callback();
          }
        })
        .catch(err => {
          console.error('获取订单详情失败', err);
          wx.showToast({
            title: '获取订单详情失败',
            icon: 'none'
          });
          wx.hideLoading();
        });
    }
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 邀请好友
  inviteFriends: function() {
    wx.showToast({
      title: '邀请好友功能开发中',
      icon: 'none'
    });
  },

  // 查看更多 - 切换气泡菜单显示状态
  toggleMore: function() {
    this.setData({
      showMoreOptions: !this.data.showMoreOptions
    });

    // 点击其他区域关闭气泡
    if (this.data.showMoreOptions) {
      setTimeout(() => {
        const hideMenu = () => {
          this.setData({
            showMoreOptions: false
          });
          wx.offTouchStart(hideMenu);
        };
        wx.onTouchStart(hideMenu);
      }, 100);
    }
  },

  // 联系客服
  contactService: function() {
    // 隐藏气泡菜单
    this.setData({
      showMoreOptions: false
    });

    wx.showToast({
      title: '联系客服功能开发中',
      icon: 'none'
    });
  },

  // 返回首页
  backToHome: function() {
    // 隐藏气泡菜单
    this.setData({
      showMoreOptions: false
    });

    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 扫码签到
  scanToEnter: function() {
    // 隐藏气泡菜单（如果显示的话）
    if (this.data.showMoreOptions) {
      this.setData({
        showMoreOptions: false
      });
    }

    // 调用微信扫码API
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res);
        // 演示模式，扫任意码都可以
        this.handleScanResult();
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理扫码结果
  handleScanResult: function() {
    const { order } = this.data;
    if (!order || !order.booking || !order.booking.sessions || order.booking.sessions.length === 0) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    // 获取当前时间
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // 解析订单日期
    const orderDateParts = order.booking.date.split('.');
    const orderDate = new Date(
      parseInt(orderDateParts[0]),
      parseInt(orderDateParts[1]) - 1,
      parseInt(orderDateParts[2])
    );

    // 判断是否是今天的订单
    const isToday =
      now.getFullYear() === orderDate.getFullYear() &&
      now.getMonth() === orderDate.getMonth() &&
      now.getDate() === orderDate.getDate();

    // 查找当前时间段的场次或最近的场次
    let targetSession = null;
    let minTimeDiff = Infinity;

    order.booking.sessions.forEach(session => {
      // 解析时间段 (格式可能是 "11:00-12:00" 或 "11:00")
      let timeStr = session.time;
      // 确保我们有一个时间范围字符串
      if (!timeStr.includes('-')) {
        timeStr = timeStr + '-' + (parseInt(timeStr.split(':')[0]) + 1) + ':' + timeStr.split(':')[1];
      }

      const timeRange = timeStr.split('-');
      const startTimeParts = timeRange[0].trim().split(':');
      const startHour = parseInt(startTimeParts[0]);
      const startMinute = parseInt(startTimeParts[1] || 0);

      let endHour = startHour + 1; // 默认一小时
      let endMinute = startMinute;

      if (timeRange.length > 1) {
        const endTimeParts = timeRange[1].trim().split(':');
        endHour = parseInt(endTimeParts[0]);
        endMinute = parseInt(endTimeParts[1] || 0);
      }

      // 计算当前时间与场次开始时间的差异（分钟）
      const sessionStartMinutes = startHour * 60 + startMinute;
      const currentMinutes = currentHour * 60 + currentMinute;
      const timeDiff = sessionStartMinutes - currentMinutes;

      // 判断当前时间是否在场次时间范围内
      const isInTimeRange = isToday &&
        currentHour >= startHour && currentMinute >= startMinute &&
        (currentHour < endHour || (currentHour === endHour && currentMinute < endMinute));

      // 如果在时间范围内，直接选择该场次
      if (isInTimeRange) {
        targetSession = session;
        minTimeDiff = 0;
      }
      // 否则，找最近的未开始场次
      else if (timeDiff > 0 && timeDiff < minTimeDiff && session.status !== '已完成') {
        targetSession = session;
        minTimeDiff = timeDiff;
      }
    });

    // 如果没有找到合适的场次，选择第一个未完成的场次
    if (!targetSession) {
      targetSession = order.booking.sessions.find(s => s.status !== '已完成') || order.booking.sessions[0];
    }

    // 设置当前场次并显示签到弹窗
    this.setData({
      currentSession: targetSession,
      showSignInModal: true,
      countDown: 5,
      canSignIn: false
    });

    // 开始倒计时
    this.startCountDown();
  },

  // 开始倒计时
  startCountDown: function() {
    const countDownInterval = setInterval(() => {
      let count = this.data.countDown - 1;

      if (count <= 0) {
        clearInterval(countDownInterval);
        this.setData({
          countDown: 0,
          canSignIn: true
        });
      } else {
        this.setData({
          countDown: count
        });
      }
    }, 1000);
  },

  // 确认签到
  confirmSignIn: function() {
    if (!this.data.canSignIn) return;

    const { order, currentSession } = this.data;
    if (!order || !currentSession) return;

    console.log('确认签到 - 当前订单:', order);
    console.log('确认签到 - 当前场次:', currentSession);

    // 找到当前场次在订单中的索引
    const sessionIndex = order.booking.sessions.findIndex(
      s => s.time === currentSession.time && s.area === currentSession.area
    );

    console.log('确认签到 - 场次索引:', sessionIndex);

    if (sessionIndex === -1) {
      console.log('确认签到 - 未找到匹配的场次');
      return;
    }

    // 更新场次状态为已完成
    const updatedOrder = JSON.parse(JSON.stringify(order)); // 深拷贝
    updatedOrder.booking.sessions[sessionIndex].status = '已完成';

    // 更新本地存储中的订单
    updateOrderById(order.id, updatedOrder);

    // 更新页面数据
    this.setData({
      order: updatedOrder,
      showSignInModal: false
    });

    wx.showToast({
      title: '签到成功',
      icon: 'success'
    });
  },

  // 关闭签到弹窗
  closeSignInModal: function() {
    this.setData({
      showSignInModal: false
    });
  },

  // 显示订场说明
  showBookingExplanation: function() {
    wx.showModal({
      title: '订场说明',
      content: '1. 预订成功后，请按时到场，迟到30分钟将视为自动放弃，不予退款。\n2. 场地使用期间，请遵守场馆规定，爱护场地设施。\n3. 如需取消预订，请提前4小时，否则将收取全额费用。\n4. 恶劣天气可能导致场地关闭，届时将全额退款或安排改期。\n5. 如有任何问题，请联系客服：400-123-4567。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 分享
  onShareAppMessage: function() {
    const { order } = this.data;
    return {
      title: `我预订了${order.venue.name}的场地，一起来打球吧！`,
      path: `/pages/order-detail/order-detail?orderId=${order.id}`,
      imageUrl: order.venue.imageUrl
    };
  }
});
